import proj4 from 'proj4';
import { config } from '../config';
import { logger } from './logging';

// Define common projections
proj4.defs('EPSG:4326', '+proj=longlat +datum=WGS84 +no_defs');
proj4.defs('EPSG:3857', '+proj=merc +a=6378137 +b=6378137 +lat_ts=0.0 +lon_0=0.0 +x_0=0.0 +y_0=0 +k=1.0 +units=m +nadgrids=@null +wktext +no_defs');

export interface Coordinate {
  x: number;
  y: number;
}

export interface BoundingBox {
  minX: number;
  minY: number;
  maxX: number;
  maxY: number;
}

export interface GeoJSONGeometry {
  type: string;
  coordinates: any;
}

/**
 * Transform coordinates from one SRS to another
 */
export function transformCoordinate(
  coord: Coordinate,
  fromSrs: string,
  toSrs: string
): Coordinate {
  try {
    const result = proj4(fromSrs, toSrs, [coord.x, coord.y]);
    return { x: result[0], y: result[1] };
  } catch (error) {
    logger.error('Coordinate transformation failed', {
      fromSrs,
      toSrs,
      coordinate: coord,
      error: error instanceof Error ? error.message : String(error)
    });
    throw new Error(`Failed to transform coordinate from ${fromSrs} to ${toSrs}`);
  }
}

/**
 * Transform bounding box from one SRS to another
 */
export function transformBoundingBox(
  bbox: BoundingBox,
  fromSrs: string,
  toSrs: string
): BoundingBox {
  try {
    // Transform all four corners to handle rotation/skew
    const corners = [
      { x: bbox.minX, y: bbox.minY },
      { x: bbox.maxX, y: bbox.minY },
      { x: bbox.maxX, y: bbox.maxY },
      { x: bbox.minX, y: bbox.maxY }
    ];

    const transformedCorners = corners.map(corner => 
      transformCoordinate(corner, fromSrs, toSrs)
    );

    // Find new min/max
    const xs = transformedCorners.map(c => c.x);
    const ys = transformedCorners.map(c => c.y);

    return {
      minX: Math.min(...xs),
      minY: Math.min(...ys),
      maxX: Math.max(...xs),
      maxY: Math.max(...ys)
    };
  } catch (error) {
    logger.error('Bounding box transformation failed', {
      fromSrs,
      toSrs,
      bbox,
      error: error instanceof Error ? error.message : String(error)
    });
    throw new Error(`Failed to transform bounding box from ${fromSrs} to ${toSrs}`);
  }
}

/**
 * Transform GeoJSON geometry from one SRS to another
 */
export function transformGeoJSONGeometry(
  geometry: GeoJSONGeometry,
  fromSrs: string,
  toSrs: string
): GeoJSONGeometry {
  if (fromSrs === toSrs) {
    return geometry;
  }

  try {
    const transformed = { ...geometry };
    transformed.coordinates = transformCoordinates(geometry.coordinates, geometry.type, fromSrs, toSrs);
    return transformed;
  } catch (error) {
    logger.error('GeoJSON geometry transformation failed', {
      fromSrs,
      toSrs,
      geometryType: geometry.type,
      error: error instanceof Error ? error.message : String(error)
    });
    throw new Error(`Failed to transform geometry from ${fromSrs} to ${toSrs}`);
  }
}

function transformCoordinates(coordinates: any, geometryType: string, fromSrs: string, toSrs: string): any {
  switch (geometryType) {
    case 'Point':
      const [x, y] = coordinates;
      const transformed = proj4(fromSrs, toSrs, [x, y]);
      return [transformed[0], transformed[1]];

    case 'LineString':
    case 'MultiPoint':
      return coordinates.map((coord: [number, number]) => {
        const transformed = proj4(fromSrs, toSrs, coord);
        return [transformed[0], transformed[1]];
      });

    case 'Polygon':
    case 'MultiLineString':
      return coordinates.map((ring: [number, number][]) =>
        ring.map((coord: [number, number]) => {
          const transformed = proj4(fromSrs, toSrs, coord);
          return [transformed[0], transformed[1]];
        })
      );

    case 'MultiPolygon':
      return coordinates.map((polygon: [number, number][][]) =>
        polygon.map((ring: [number, number][]) =>
          ring.map((coord: [number, number]) => {
            const transformed = proj4(fromSrs, toSrs, coord);
            return [transformed[0], transformed[1]];
          })
        )
      );

    default:
      throw new Error(`Unsupported geometry type: ${geometryType}`);
  }
}

/**
 * Convert GeoJSON geometry to WKT format
 */
export function geometryToWKT(geometry: GeoJSONGeometry): string {
  switch (geometry.type) {
    case 'Point':
      const [x, y] = geometry.coordinates;
      return `POINT(${x} ${y})`;

    case 'LineString':
      const lineCoords = geometry.coordinates
        .map((coord: [number, number]) => `${coord[0]} ${coord[1]}`)
        .join(', ');
      return `LINESTRING(${lineCoords})`;

    case 'Polygon':
      const rings = geometry.coordinates
        .map((ring: [number, number][]) => {
          const ringCoords = ring
            .map((coord: [number, number]) => `${coord[0]} ${coord[1]}`)
            .join(', ');
          return `(${ringCoords})`;
        })
        .join(', ');
      return `POLYGON(${rings})`;

    case 'MultiPoint':
      const points = geometry.coordinates
        .map((coord: [number, number]) => `(${coord[0]} ${coord[1]})`)
        .join(', ');
      return `MULTIPOINT(${points})`;

    case 'MultiLineString':
      const lines = geometry.coordinates
        .map((line: [number, number][]) => {
          const lineCoords = line
            .map((coord: [number, number]) => `${coord[0]} ${coord[1]}`)
            .join(', ');
          return `(${lineCoords})`;
        })
        .join(', ');
      return `MULTILINESTRING(${lines})`;

    case 'MultiPolygon':
      const polygons = geometry.coordinates
        .map((polygon: [number, number][][]) => {
          const rings = polygon
            .map((ring: [number, number][]) => {
              const ringCoords = ring
                .map((coord: [number, number]) => `${coord[0]} ${coord[1]}`)
                .join(', ');
              return `(${ringCoords})`;
            })
            .join(', ');
          return `(${rings})`;
        })
        .join(', ');
      return `MULTIPOLYGON(${polygons})`;

    default:
      throw new Error(`Unsupported geometry type: ${geometry.type}`);
  }
}

/**
 * Simplify geometry using Douglas-Peucker algorithm
 */
export function simplifyGeometry(
  geometry: GeoJSONGeometry,
  tolerance: number = config.aoi.simplificationTolerance
): GeoJSONGeometry {
  // For now, return as-is. In production, you'd implement Douglas-Peucker
  // or use a library like Turf.js
  return geometry;
}

/**
 * Handle WMS 1.3.0 axis order for geographic SRS
 */
export function formatBboxForWms(
  bbox: BoundingBox,
  srs: string,
  wmsVersion: string = config.geoserver.wmsVersion
): string {
  // WMS 1.3.0 uses lat/lon order for geographic SRS (EPSG:4326)
  if (wmsVersion === '1.3.0' && srs === 'EPSG:4326') {
    return `${bbox.minY},${bbox.minX},${bbox.maxY},${bbox.maxX}`;
  }
  
  // Default lon/lat order
  return `${bbox.minX},${bbox.minY},${bbox.maxX},${bbox.maxY}`;
}

/**
 * Parse BBOX string into BoundingBox object, handling axis order
 */
export function parseBboxFromWms(
  bboxString: string,
  srs: string,
  wmsVersion: string = config.geoserver.wmsVersion
): BoundingBox {
  const values = bboxString.split(',').map(Number);
  
  if (values.length !== 4) {
    throw new Error('Invalid BBOX format');
  }

  // WMS 1.3.0 uses lat/lon order for geographic SRS (EPSG:4326)
  if (wmsVersion === '1.3.0' && srs === 'EPSG:4326') {
    return {
      minX: values[1], // lon
      minY: values[0], // lat
      maxX: values[3], // lon
      maxY: values[2]  // lat
    };
  }
  
  // Default lon/lat order
  return {
    minX: values[0],
    minY: values[1],
    maxX: values[2],
    maxY: values[3]
  };
}

/**
 * Calculate geometry bounds
 */
export function getGeometryBounds(geometry: GeoJSONGeometry): BoundingBox {
  const coords = getAllCoordinates(geometry);
  const xs = coords.map(c => c[0]);
  const ys = coords.map(c => c[1]);
  
  return {
    minX: Math.min(...xs),
    minY: Math.min(...ys),
    maxX: Math.max(...xs),
    maxY: Math.max(...ys)
  };
}

function getAllCoordinates(geometry: GeoJSONGeometry): [number, number][] {
  const coords: [number, number][] = [];
  
  function extractCoords(coordinates: any, level: number = 0): void {
    if (level === 0 && geometry.type === 'Point') {
      coords.push(coordinates);
    } else if (level <= 1 && (geometry.type === 'LineString' || geometry.type === 'MultiPoint')) {
      for (const coord of coordinates) {
        if (level === 0) {
          coords.push(coord);
        } else {
          extractCoords(coord, level - 1);
        }
      }
    } else if (level <= 2 && (geometry.type === 'Polygon' || geometry.type === 'MultiLineString')) {
      for (const ring of coordinates) {
        extractCoords(ring, level - 1);
      }
    } else if (level <= 3 && geometry.type === 'MultiPolygon') {
      for (const polygon of coordinates) {
        extractCoords(polygon, level - 1);
      }
    }
  }
  
  extractCoords(geometry.coordinates);
  return coords;
}
