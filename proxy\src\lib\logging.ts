import { config } from '../config';

export interface Logger {
  error: (message: string, meta?: any) => void;
  warn: (message: string, meta?: any) => void;
  info: (message: string, meta?: any) => void;
  debug: (message: string, meta?: any) => void;
}

export interface LogEntry {
  timestamp: string;
  level: string;
  message: string;
  meta?: any;
  traceId?: string;
}

class ConsoleLogger implements Logger {
  private shouldLog(level: string): boolean {
    const levels = ['error', 'warn', 'info', 'debug'];
    const configLevel = config.app.logLevel;
    const configIndex = levels.indexOf(configLevel);
    const messageIndex = levels.indexOf(level);
    return messageIndex <= configIndex;
  }

  private formatLog(level: string, message: string, meta?: any): LogEntry {
    const entry: LogEntry = {
      timestamp: new Date().toISOString(),
      level,
      message
    };

    if (meta) {
      // Redact sensitive information
      entry.meta = this.redactSensitive(meta);
    }

    return entry;
  }

  private redactSensitive(obj: any): any {
    if (typeof obj !== 'object' || obj === null) {
      return obj;
    }

    const sensitive = ['password', 'authorization', 'auth', 'secret', 'token', 'key'];
    const redacted = { ...obj };

    for (const [key, value] of Object.entries(redacted)) {
      const lowerKey = key.toLowerCase();
      if (sensitive.some(s => lowerKey.includes(s))) {
        redacted[key] = '[REDACTED]';
      } else if (typeof value === 'object' && value !== null) {
        redacted[key] = this.redactSensitive(value);
      }
    }

    return redacted;
  }

  private output(entry: LogEntry): void {
    if (config.app.nodeEnv === 'production') {
      // JSON format for production
      console.log(JSON.stringify(entry));
    } else {
      // Human-readable format for development
      const { timestamp, level, message, meta } = entry;
      const levelColors = {
        error: '\x1b[31m', // red
        warn: '\x1b[33m',  // yellow
        info: '\x1b[36m',  // cyan
        debug: '\x1b[90m'  // gray
      };
      const resetColor = '\x1b[0m';
      const color = levelColors[level as keyof typeof levelColors] || '';
      
      let output = `${timestamp} ${color}[${level.toUpperCase()}]${resetColor} ${message}`;
      if (meta) {
        output += '\n' + JSON.stringify(meta, null, 2);
      }
      console.log(output);
    }
  }

  error(message: string, meta?: any): void {
    if (!this.shouldLog('error')) return;
    this.output(this.formatLog('error', message, meta));
  }

  warn(message: string, meta?: any): void {
    if (!this.shouldLog('warn')) return;
    this.output(this.formatLog('warn', message, meta));
  }

  info(message: string, meta?: any): void {
    if (!this.shouldLog('info')) return;
    this.output(this.formatLog('info', message, meta));
  }

  debug(message: string, meta?: any): void {
    if (!this.shouldLog('debug')) return;
    this.output(this.formatLog('debug', message, meta));
  }
}

// Create singleton logger instance
export const logger = new ConsoleLogger();

// Utility functions for common logging patterns
export function logRequest(req: any, res: any, duration?: number): void {
  const meta = {
    method: req.method,
    url: req.url,
    statusCode: res.statusCode,
    userAgent: req.get('User-Agent'),
    ip: req.ip,
    ...(duration && { durationMs: duration })
  };

  const level = res.statusCode >= 400 ? 'warn' : 'info';
  const message = `${req.method} ${req.url} - ${res.statusCode}`;
  
  if (level === 'warn') {
    logger.warn(message, meta);
  } else {
    logger.info(message, meta);
  }
}

export function logClipping(params: {
  layer: string;
  tier: string;
  reason: string;
  durationMs?: number;
  aoiId?: string;
  bytes?: number;
}): void {
  logger.info('Clipping operation completed', params);
}

export function logDiscovery(params: {
  operation: string;
  serviceId?: string;
  layersFound?: number;
  durationMs?: number;
  error?: string;
}): void {
  if (params.error) {
    logger.error(`Discovery ${params.operation} failed`, params);
  } else {
    logger.info(`Discovery ${params.operation} completed`, params);
  }
}

export function logGeoServerRequest(params: {
  serviceId: string;
  method: string;
  url: string;
  statusCode?: number;
  durationMs?: number;
  error?: string;
}): void {
  // Redact URL if it contains credentials
  const safeUrl = params.url.replace(/(:\/\/)[^@]*@/, '$1[REDACTED]@');
  
  const meta = {
    ...params,
    url: safeUrl
  };

  if (params.error || (params.statusCode && params.statusCode >= 400)) {
    logger.error('GeoServer request failed', meta);
  } else {
    logger.debug('GeoServer request completed', meta);
  }
}

// Create trace ID for request correlation
export function generateTraceId(): string {
  return Math.random().toString(36).substring(2, 15) + 
         Math.random().toString(36).substring(2, 15);
}
