import { v4 as uuidv4 } from 'uuid';
import crypto from 'crypto';
import { config } from '../config';
import { logger } from '../lib/logging';
import { aoiCacheOps } from '../lib/cache';
import {
  transformGeoJSONGeometry,
  geometryToWKT,
  simplifyGeometry,
  getGeometryBounds,
  BoundingBox,
  GeoJSONGeometry
} from '../lib/reprojection';

export interface AOI {
  id: string;
  geometry: GeoJSONGeometry;
  bounds: BoundingBox;
  centroid: [number, number];
  area: number;
  vertices: number;
  hash: string;
  created: Date;
  srs: string;
}

export interface AOIInput {
  type: 'FeatureCollection' | 'Feature' | 'Polygon' | 'MultiPolygon';
  geometry?: GeoJSONGeometry;
  features?: Array<{ geometry: GeoJSONGeometry }>;
  coordinates?: any;
  srs?: string;
}

/**
 * Validate AOI input geometry
 */
export function validateAOIGeometry(input: AOIInput): GeoJSONGeometry {
  let geometry: GeoJSONGeometry;

  // Extract geometry from different input formats
  if (input.type === 'FeatureCollection') {
    if (!input.features || input.features.length === 0) {
      throw new Error('FeatureCollection must contain at least one feature');
    }
    
    if (input.features.length === 1) {
      geometry = input.features[0].geometry;
    } else {
      // Combine multiple features into a GeometryCollection
      throw new Error('Multiple features not supported yet. Please use a single Polygon or MultiPolygon');
    }
  } else if (input.type === 'Feature') {
    if (!input.geometry) {
      throw new Error('Feature must have a geometry');
    }
    geometry = input.geometry;
  } else if (input.type === 'Polygon' || input.type === 'MultiPolygon') {
    geometry = {
      type: input.type,
      coordinates: input.coordinates
    };
  } else {
    throw new Error(`Unsupported geometry type: ${input.type}`);
  }

  // Validate geometry type
  if (!['Polygon', 'MultiPolygon'].includes(geometry.type)) {
    throw new Error(`AOI must be Polygon or MultiPolygon, got: ${geometry.type}`);
  }

  // Validate coordinates structure
  if (!geometry.coordinates || !Array.isArray(geometry.coordinates)) {
    throw new Error('Invalid geometry coordinates');
  }

  // Count vertices and validate limits
  const vertices = countVertices(geometry);
  if (vertices > config.aoi.maxVertices) {
    throw new Error(`AOI has too many vertices: ${vertices} > ${config.aoi.maxVertices}`);
  }

  // Validate polygon closure
  validatePolygonClosure(geometry);

  return geometry;
}

/**
 * Count total vertices in geometry
 */
function countVertices(geometry: GeoJSONGeometry): number {
  let count = 0;

  if (geometry.type === 'Polygon') {
    for (const ring of geometry.coordinates) {
      count += ring.length;
    }
  } else if (geometry.type === 'MultiPolygon') {
    for (const polygon of geometry.coordinates) {
      for (const ring of polygon) {
        count += ring.length;
      }
    }
  }

  return count;
}

/**
 * Validate that polygon rings are properly closed
 */
function validatePolygonClosure(geometry: GeoJSONGeometry): void {
  const validateRing = (ring: [number, number][]): void => {
    if (ring.length < 4) {
      throw new Error('Polygon ring must have at least 4 coordinates');
    }
    
    const first = ring[0];
    const last = ring[ring.length - 1];
    
    if (first[0] !== last[0] || first[1] !== last[1]) {
      throw new Error('Polygon ring is not closed (first and last coordinates must be the same)');
    }
  };

  if (geometry.type === 'Polygon') {
    for (const ring of geometry.coordinates) {
      validateRing(ring);
    }
  } else if (geometry.type === 'MultiPolygon') {
    for (const polygon of geometry.coordinates) {
      for (const ring of polygon) {
        validateRing(ring);
      }
    }
  }
}

/**
 * Calculate centroid of geometry
 */
function calculateCentroid(geometry: GeoJSONGeometry): [number, number] {
  const bounds = getGeometryBounds(geometry);
  return [
    (bounds.minX + bounds.maxX) / 2,
    (bounds.minY + bounds.maxY) / 2
  ];
}

/**
 * Calculate approximate area using shoelace formula
 */
function calculateArea(geometry: GeoJSONGeometry): number {
  let totalArea = 0;

  const calculatePolygonArea = (coordinates: [number, number][][]): number => {
    let area = 0;
    
    // Only calculate area for exterior ring (first ring)
    const ring = coordinates[0];
    
    for (let i = 0; i < ring.length - 1; i++) {
      const [x1, y1] = ring[i];
      const [x2, y2] = ring[i + 1];
      area += (x1 * y2) - (x2 * y1);
    }
    
    return Math.abs(area) / 2;
  };

  if (geometry.type === 'Polygon') {
    totalArea = calculatePolygonArea(geometry.coordinates);
  } else if (geometry.type === 'MultiPolygon') {
    for (const polygon of geometry.coordinates) {
      totalArea += calculatePolygonArea(polygon);
    }
  }

  return totalArea;
}

/**
 * Generate hash for AOI geometry (for caching)
 */
function generateAOIHash(geometry: GeoJSONGeometry, srs: string): string {
  const content = JSON.stringify({ geometry, srs });
  return crypto.createHash('sha256').update(content).digest('hex').substring(0, 16);
}

/**
 * Create AOI from input
 */
export function createAOI(input: AOIInput): AOI {
  const startTime = Date.now();
  
  try {
    // Validate and extract geometry
    let geometry = validateAOIGeometry(input);
    
    const inputSrs = input.srs || config.aoi.defaultSrs;
    
    // Simplify if necessary
    const vertices = countVertices(geometry);
    if (vertices > 1000) { // Simplify large geometries
      geometry = simplifyGeometry(geometry, config.aoi.simplificationTolerance);
      logger.debug('AOI geometry simplified', {
        originalVertices: vertices,
        simplifiedVertices: countVertices(geometry)
      });
    }

    // Calculate properties
    const bounds = getGeometryBounds(geometry);
    const centroid = calculateCentroid(geometry);
    const area = calculateArea(geometry);
    const hash = generateAOIHash(geometry, inputSrs);

    // Create AOI object
    const aoi: AOI = {
      id: uuidv4(),
      geometry,
      bounds,
      centroid,
      area,
      vertices: countVertices(geometry),
      hash,
      created: new Date(),
      srs: inputSrs
    };

    // Cache the AOI
    aoiCacheOps.set(aoi.id, aoi);

    logger.info('AOI created', {
      aoiId: aoi.id,
      vertices: aoi.vertices,
      area: aoi.area,
      srs: inputSrs,
      durationMs: Date.now() - startTime
    });

    return aoi;
  } catch (error) {
    logger.error('AOI creation failed', {
      error: error instanceof Error ? error.message : String(error),
      durationMs: Date.now() - startTime
    });
    throw error;
  }
}

/**
 * Get AOI by ID
 */
export function getAOI(aoiId: string): AOI | null {
  return aoiCacheOps.get(aoiId) || null;
}

/**
 * Get AOI geometry in target SRS
 */
export function getAOIGeometry(aoiId: string, targetSrs: string): GeoJSONGeometry | null {
  const aoi = getAOI(aoiId);
  if (!aoi) return null;

  // Return cached geometry if SRS matches
  if (aoi.srs === targetSrs) {
    return aoi.geometry;
  }

  // Transform to target SRS
  try {
    const transformed = transformGeoJSONGeometry(aoi.geometry, aoi.srs, targetSrs);
    
    logger.debug('AOI geometry transformed', {
      aoiId,
      fromSrs: aoi.srs,
      toSrs: targetSrs
    });

    return transformed;
  } catch (error) {
    logger.error('AOI geometry transformation failed', {
      aoiId,
      fromSrs: aoi.srs,
      toSrs: targetSrs,
      error: error instanceof Error ? error.message : String(error)
    });
    return null;
  }
}

/**
 * Get AOI as WKT in target SRS
 */
export function getAOIAsWKT(aoiId: string, targetSrs: string): string | null {
  const geometry = getAOIGeometry(aoiId, targetSrs);
  if (!geometry) return null;

  try {
    const wkt = geometryToWKT(geometry);
    
    // Check if WKT is too long and might need simplification
    if (wkt.length > config.aoi.wktLengthThreshold) {
      logger.warn('WKT is very long, consider simplifying geometry', {
        aoiId,
        wktLength: wkt.length,
        threshold: config.aoi.wktLengthThreshold
      });
    }

    return wkt;
  } catch (error) {
    logger.error('AOI WKT generation failed', {
      aoiId,
      targetSrs,
      error: error instanceof Error ? error.message : String(error)
    });
    return null;
  }
}

/**
 * Get AOI bounds in target SRS
 */
export function getAOIBounds(aoiId: string, targetSrs: string): BoundingBox | null {
  const geometry = getAOIGeometry(aoiId, targetSrs);
  if (!geometry) return null;

  try {
    return getGeometryBounds(geometry);
  } catch (error) {
    logger.error('AOI bounds calculation failed', {
      aoiId,
      targetSrs,
      error: error instanceof Error ? error.message : String(error)
    });
    return null;
  }
}

/**
 * Generate hash for AOI in specific SRS (for caching)
 */
export function getAOIHash(aoiId: string, targetSrs?: string): string | null {
  const aoi = getAOI(aoiId);
  if (!aoi) return null;

  if (!targetSrs || targetSrs === aoi.srs) {
    return aoi.hash;
  }

  // Generate hash for transformed geometry
  const geometry = getAOIGeometry(aoiId, targetSrs);
  if (!geometry) return null;

  return generateAOIHash(geometry, targetSrs);
}

/**
 * List all cached AOIs (for debugging/monitoring)
 */
export function listAOIs(): Array<{ id: string; vertices: number; area: number; created: Date; srs: string }> {
  const stats = aoiCacheOps.getStats();
  logger.debug('AOI cache stats requested', stats);

  // In a real implementation, you might want to iterate through cache keys
  // For now, return empty array as we don't expose internal cache structure
  return [];
}

/**
 * Clear AOI cache
 */
export function clearAOICache(): void {
  aoiCacheOps.clear();
  logger.info('AOI cache cleared');
}

/**
 * Validate AOI exists and return basic info
 */
export function validateAOIExists(aoiId: string): { exists: boolean; aoi?: AOI } {
  const aoi = getAOI(aoiId);
  return {
    exists: !!aoi,
    aoi: aoi || undefined
  };
}
