import { config } from '../config';
import { logger, logClipping } from '../lib/logging';
import { getAOIAsWKT, getAOI } from './aoi';

export interface VectorClippingParams {
  layer: string;
  aoiId: string;
  geometryField?: string;
  targetSrs: string;
  serviceId: string;
}

export interface VectorClippingResult {
  cqlFilter?: string;
  tier: 'cql' | 'bbox';
  reason: string;
  params: Record<string, string>;
}

/**
 * Build CQL filter for vector clipping
 */
export function buildVectorClipping(params: VectorClippingParams): VectorClippingResult {
  const startTime = Date.now();
  
  try {
    // Check if CQL is enabled
    if (!config.clipping.vectorAllowCql) {
      return buildBboxClipping(params, 'cql-disabled');
    }

    // Check if geometry field is known
    if (!params.geometryField) {
      logger.debug('No geometry field available, falling back to bbox', {
        layer: params.layer,
        aoiId: params.aoiId
      });
      return buildBboxClipping(params, 'no-geomfield');
    }

    // Get AOI as WKT
    const aoiWkt = getAOIAsWKT(params.aoiId, params.targetSrs);
    if (!aoiWkt) {
      logger.error('Failed to get AOI WKT for CQL filter', {
        aoiId: params.aoiId,
        targetSrs: params.targetSrs
      });
      return buildBboxClipping(params, 'wkt-failed');
    }

    // Check WKT length
    if (aoiWkt.length > config.aoi.wktLengthThreshold) {
      logger.warn('WKT too long for CQL, falling back to bbox', {
        aoiId: params.aoiId,
        wktLength: aoiWkt.length,
        threshold: config.aoi.wktLengthThreshold
      });
      return buildBboxClipping(params, 'wkt-too-long');
    }

    // Build CQL INTERSECTS filter
    const cqlFilter = `INTERSECTS(${params.geometryField}, ${aoiWkt})`;

    const result: VectorClippingResult = {
      cqlFilter,
      tier: 'cql',
      reason: 'cql-ok',
      params: {
        CQL_FILTER: cqlFilter
      }
    };

    logClipping({
      layer: params.layer,
      tier: 'cql',
      reason: 'cql-ok',
      aoiId: params.aoiId,
      durationMs: Date.now() - startTime
    });

    return result;
  } catch (error) {
    logger.error('CQL filter creation failed', {
      layer: params.layer,
      aoiId: params.aoiId,
      error: error instanceof Error ? error.message : String(error)
    });
    return buildBboxClipping(params, 'cql-error');
  }
}

/**
 * Fallback to bbox clipping
 */
function buildBboxClipping(params: VectorClippingParams, reason: string): VectorClippingResult {
  const aoi = getAOI(params.aoiId);
  if (!aoi) {
    throw new Error(`AOI not found: ${params.aoiId}`);
  }

  // Get AOI bounds (transformed if needed)
  // For simplicity, using original bounds - in production you'd transform these
  const bounds = aoi.bounds;
  
  const result: VectorClippingResult = {
    tier: 'bbox',
    reason,
    params: {
      // BBOX parameter will be added by the calling code based on WMS version and SRS
    }
  };

  logClipping({
    layer: params.layer,
    tier: 'bbox',
    reason,
    aoiId: params.aoiId
  });

  return result;
}

/**
 * Test if CQL filter is supported for a layer
 */
export function testCQLSupport(serviceId: string, layer: string): Promise<boolean> {
  // This would test with a simple CQL filter to see if the service supports it
  // For now, assume all GeoServer instances support CQL
  return Promise.resolve(true);
}

/**
 * Validate CQL filter syntax
 */
export function validateCQLFilter(cqlFilter: string): { valid: boolean; error?: string } {
  try {
    // Basic validation - check for balanced parentheses and common functions
    const openParens = (cqlFilter.match(/\(/g) || []).length;
    const closeParens = (cqlFilter.match(/\)/g) || []).length;
    
    if (openParens !== closeParens) {
      return {
        valid: false,
        error: 'Unbalanced parentheses in CQL filter'
      };
    }

    // Check for SQL injection patterns (basic)
    const dangerousPatterns = [
      /;\s*(drop|delete|update|insert)\s+/i,
      /union\s+select/i,
      /--/,
      /\/\*/
    ];

    for (const pattern of dangerousPatterns) {
      if (pattern.test(cqlFilter)) {
        return {
          valid: false,
          error: 'Potentially dangerous pattern detected in CQL filter'
        };
      }
    }

    return { valid: true };
  } catch (error) {
    return {
      valid: false,
      error: error instanceof Error ? error.message : 'Unknown validation error'
    };
  }
}

/**
 * Optimize CQL filter for performance
 */
export function optimizeCQLFilter(cqlFilter: string): string {
  // Basic optimizations:
  // 1. Remove unnecessary whitespace
  // 2. Simplify common patterns
  
  let optimized = cqlFilter
    .replace(/\s+/g, ' ')
    .trim();

  // Add spatial index hints if supported
  if (optimized.includes('INTERSECTS')) {
    // Some databases benefit from spatial index hints
    // This is database-specific optimization
  }

  return optimized;
}

/**
 * Get recommended geometry field names for auto-detection
 */
export function getCommonGeometryFields(): string[] {
  return [
    'geom',
    'geometry',
    'the_geom',
    'shape',
    'wkb_geometry',
    'geom_col',
    'spatial_col'
  ];
}

/**
 * Build spatial filter for different geometry types
 */
export function buildSpatialFilter(
  geometryField: string,
  aoiWkt: string,
  operation: 'INTERSECTS' | 'WITHIN' | 'CONTAINS' | 'TOUCHES' = 'INTERSECTS'
): string {
  return `${operation}(${geometryField}, ${aoiWkt})`;
}

/**
 * Combine multiple CQL filters
 */
export function combineCQLFilters(filters: string[], operator: 'AND' | 'OR' = 'AND'): string {
  if (filters.length === 0) return '';
  if (filters.length === 1) return filters[0];
  
  return filters
    .map(filter => `(${filter})`)
    .join(` ${operator} `);
}
