# App
APP_PORT=3001
NODE_ENV=development
LOG_LEVEL=info
ALLOWED_ORIGINS=["http://localhost:5173","https://app.example.com"]

# GeoServer services (array of objects; no hardcoding)
# Each object: { "id","url","username","password","allowSelfSigned":true|false }
GEOSERVER_SERVICES=[
  {"id":"primary","url":"https://geoserver.example.com/geoserver","username":"user","password":"pass","allowSelfSigned":true}
]
GEOSERVER_WMS_VERSION=1.3.0
GEOSERVER_WFS_VERSION=2.0.0
GEOSERVER_WMTS_ENABLED=1
GEOSERVER_TIMEOUT_MS=15000

# AOI
AOI_MAX_VERTICES=10000
AOI_WKT_LENGTH_THRESHOLD=50000
AOI_SIMPLIFICATION_TOLERANCE=0.0001
DEFAULT_SRS=EPSG:4326

# Clipping feature flags
CLIP_VECTOR_ALLOW_CQL=1
CLIP_RASTER_ALLOW_SLD=1
CLIP_RASTER_ALLOW_WPS=1

# SLD/WPS controls
SLD_MAX_URL_BYTES=2000
WPS_TIMEOUT_MS=60000
WPS_POLL_INTERVAL_MS=1500
WPS_CACHE_DIR=.cache/wps

# Discovery
DISCOVERY_ENABLE_WMS=1
DISCOVERY_ENABLE_WFS=1
DISCOVERY_ENABLE_WMTS=1
DISCOVERY_REFRESH_SECONDS=600
DISCOVERY_PARALLEL=6

# Caching
TILE_CACHE_TTL_SECONDS=300
CAPABILITIES_TTL_SECONDS=600

# Security
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX=120
PROXY_MAX_BODY_MB=25

# TLS (for self-signed backends)
NODE_TLS_REJECT_UNAUTHORIZED=0
