# Map-Agnostic Proxy (UIEngine/Proxy v2)

A robust, configurable proxy service that centralizes discovery and AOI-true clipping for both Leaflet and OpenLayers frontends. All configuration is managed through environment variables with no hardcoded GeoServer endpoints or credentials.

## Features

- **🌍 Map-Agnostic**: Works with any frontend (Leaflet, OpenLayers, etc.)
- **🔒 Zero Hardcoding**: All configuration via `.env` with validation
- **✂️ Smart Clipping**: Multi-tier AOI clipping (CQL → SLD → WPS → mask)
- **🚀 High Performance**: Comprehensive caching with ETag support
- **🔍 Auto-Discovery**: Crawls WMS/WFS/WMTS for normalized layer manifest
- **📊 Observable**: Structured logging, metrics, health checks
- **🛡️ Secure**: Rate limiting, CORS, credential redaction

## Quick Start

### 1. Installation

```bash
cd proxy
npm install
```

### 2. Configuration

Copy the example environment file:

```bash
cp .env.example .env
```

Edit `.env` with your GeoServer configuration:

```bash
# Required: GeoServer services (JSON array)
GEOSERVER_SERVICES=[
  {
    "id": "primary",
    "url": "https://your-geoserver.com/geoserver",
    "username": "your-username",
    "password": "your-password",
    "allowSelfSigned": false
  }
]

# Optional: Override defaults
APP_PORT=3001
ALLOWED_ORIGINS=["http://localhost:5173","https://your-frontend.com"]
```

### 3. Development

```bash
# Development with hot reload
npm run dev

# Build for production
npm run build

# Start production server
npm start
```

## API Endpoints

### AOI Management

```bash
# Create AOI
POST /api/aoi
Content-Type: application/json

{
  "type": "Polygon",
  "coordinates": [[[0,0],[1,0],[1,1],[0,1],[0,0]]],
  "srs": "EPSG:4326"
}

# Response: { "aoiId": "uuid", "bounds": {...}, "centroid": [...] }
```

### Tile Services

```bash
# Vector tiles with AOI clipping
GET /api/tiles/vector/{z}/{x}/{y}.png?layer=workspace:name&aoiId=uuid&srs=EPSG:3857

# Raster tiles with AOI clipping  
GET /api/tiles/raster/{z}/{x}/{y}.png?layer=workspace:name&aoiId=uuid&srs=EPSG:3857&time=2023-01-01
```

### Discovery

```bash
# Get normalized layer manifest
GET /api/discovery/layers

# Refresh discovery (async)
POST /api/discovery/refresh

# Check job status
GET /api/jobs/{jobId}
```

### Other Services

```bash
# Legend
GET /api/legend.png?layer=workspace:name&style=default

# Feature info
GET /api/featureinfo?layer=workspace:name&aoiId=uuid&i=100&j=100&bbox=...

# AOI-clipped downloads
POST /api/downloads/aoi-true
{
  "layers": ["workspace:layer1"],
  "aoiId": "uuid",
  "format": "geotiff"
}

# Health & metrics
GET /api/health
GET /api/metrics
```

## Clipping Strategy

The proxy automatically selects the best clipping approach:

### Vector Layers
1. **CQL Filter** (preferred): `INTERSECTS(geom_field, AOI_WKT)`
2. **BBOX Fallback**: When geometry field unknown

### Raster Layers  
1. **SLD with ras:CropCoverage** (preferred): Server-side precise clipping
2. **WPS Processing**: For complex operations, cached results
3. **Visual Mask**: Client-side fallback

Response headers indicate the strategy used:
- `X-Clip-Tier: sld|wps|mask|cql|bbox`  
- `X-Clip-Reason: sld-ok|wps-timeout|no-geomfield|etc`

## Configuration

### Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `GEOSERVER_SERVICES` | **Required** | JSON array of GeoServer configs |
| `APP_PORT` | `3001` | Server port |
| `ALLOWED_ORIGINS` | `["http://localhost:5173"]` | CORS origins |
| `CLIP_VECTOR_ALLOW_CQL` | `1` | Enable CQL filtering |
| `CLIP_RASTER_ALLOW_SLD` | `1` | Enable SLD transformations |
| `CLIP_RASTER_ALLOW_WPS` | `1` | Enable WPS processing |
| `SLD_MAX_URL_BYTES` | `2000` | SLD size threshold for POST |
| `WPS_TIMEOUT_MS` | `60000` | WPS operation timeout |
| `TILE_CACHE_TTL_SECONDS` | `300` | Tile cache duration |
| `RATE_LIMIT_MAX` | `120` | Requests per window |

See `.env.example` for complete list.

### GeoServer Services

Services are defined as JSON array in `GEOSERVER_SERVICES`:

```json
[
  {
    "id": "primary",
    "url": "https://geoserver.example.com/geoserver", 
    "username": "user",
    "password": "pass",
    "allowSelfSigned": true
  },
  {
    "id": "secondary",
    "url": "https://backup.example.com/geoserver",
    "username": "user2", 
    "password": "pass2",
    "allowSelfSigned": false
  }
]
```

## Architecture

```
src/
├── app.ts                 # Express app setup
├── config.ts             # Environment validation
├── discovery/            # Service discovery
│   ├── index.ts         # Discovery orchestrator  
│   ├── normalize.ts     # Layer manifest normalization
│   ├── parser.ts        # WMS/WFS/WMTS parsing
│   └── types.ts         # Type definitions
├── clipping/            # AOI clipping strategies
│   ├── aoi.ts          # AOI management & caching
│   ├── vector.ts       # CQL filter generation
│   ├── sld.ts          # SLD transformation
│   ├── wps.ts          # WPS async processing
│   └── policy.ts       # Tier selection logic
├── tiles/              # Tile serving
│   ├── vectorRoute.ts  # Vector tile endpoint
│   └── rasterRoute.ts  # Raster tile endpoint
├── routes/             # API route handlers
│   ├── aoi.ts         # AOI CRUD operations
│   ├── discovery.ts   # Discovery endpoints
│   ├── health.ts      # Health & metrics
│   └── downloads.ts   # AOI-true downloads
└── lib/               # Core utilities
    ├── geoserver.ts   # HTTP client
    ├── cache.ts       # Multi-tier caching
    ├── logging.ts     # Structured logging
    └── reprojection.ts # Coordinate transforms
```

## Security

- **Credential Protection**: Never logs passwords or auth tokens
- **Rate Limiting**: Configurable per-window limits  
- **CORS**: Restrict origins in production
- **Input Validation**: Zod schema validation
- **TLS**: Support for self-signed certificates per service

## Monitoring

### Health Check
```bash
curl http://localhost:3001/api/health
```

Returns service status, cache statistics, and configuration summary.

### Metrics (Prometheus)
```bash
curl http://localhost:3001/api/metrics
```

Exports cache hit/miss ratios, memory usage, and uptime.

### Structured Logging

All logs include:
- Trace ID for request correlation
- Service identification  
- Performance metrics
- Redacted sensitive data

Example log entry:
```json
{
  "timestamp": "2023-01-01T12:00:00.000Z",
  "level": "info", 
  "message": "Clipping operation completed",
  "meta": {
    "layer": "workspace:layer",
    "tier": "sld",
    "reason": "sld-ok", 
    "durationMs": 45,
    "aoiId": "uuid",
    "traceId": "abc123"
  }
}
```

## Development

### Scripts

```bash
npm run dev      # Development server with hot reload
npm run build    # TypeScript compilation
npm run start    # Production server
npm run test     # Run tests
npm run lint     # ESLint checking
npm run clean    # Clean build directory
```

### Project Structure

The codebase follows clean architecture principles:

- **Routes**: HTTP interface layer
- **Services**: Business logic (discovery, clipping)
- **Lib**: Infrastructure (HTTP, caching, logging)
- **Config**: Environment management

### Testing

```bash
# Run all tests
npm test

# Test specific module
npm test -- --testPathPattern=aoi

# Coverage report
npm test -- --coverage
```

## Production Deployment

### Docker

```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY dist/ ./dist/
EXPOSE 3001
CMD ["node", "dist/app.js"]
```

### Environment

- Set `NODE_ENV=production`
- Configure proper CORS origins
- Use secure GeoServer credentials
- Enable structured logging
- Set up monitoring and alerting

### Performance

- Enable HTTP/2 in reverse proxy
- Configure appropriate cache TTLs
- Monitor WPS cache disk usage
- Set up log rotation

## Troubleshooting

### Common Issues

**Connection refused to GeoServer**
- Verify `GEOSERVER_SERVICES` URL and credentials
- Check network connectivity
- Review `allowSelfSigned` setting for self-signed certificates

**AOI clipping not working**  
- Check `CLIP_*_ALLOW_*` feature flags
- Verify AOI geometry is valid (closed polygons)
- Review clipping headers in response: `X-Clip-Tier` and `X-Clip-Reason`

**Discovery not finding layers**
- Ensure `DISCOVERY_ENABLE_*` flags are set
- Check GeoServer WMS/WFS capabilities are accessible
- Review discovery logs for parsing errors

**High memory usage**
- Reduce cache TTL values
- Lower `WPS_CACHE_DIR` max size  
- Monitor cache statistics via `/api/health`

### Debug Logging

Set `LOG_LEVEL=debug` to enable verbose logging:

```bash
LOG_LEVEL=debug npm run dev
```

## License

MIT License - see LICENSE file for details.

## Contributing

1. Fork the repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

## Support

For issues and questions:
- Check existing [GitHub Issues](issues)
- Review troubleshooting section above
- Create new issue with:
  - Environment details (`NODE_ENV`, versions)
  - Configuration (redacted credentials)
  - Logs with trace IDs
  - Steps to reproduce
