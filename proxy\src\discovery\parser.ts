import { XMLParser } from 'fast-xml-parser';
import { config } from '../config';
import { logger, logDiscovery } from '../lib/logging';
import { getCapabilities, describeFeatureType } from '../lib/geoserver';
import { LayerManifest, LayerInfo, ServiceCapabilities } from './types';

const xmlParser = new XMLParser({
  ignoreAttributes: false,
  attributeNamePrefix: '@_',
  parseAttributeValue: true,
  trimValues: true
});

/**
 * Parse WMS capabilities and extract layer information
 */
export async function parseWMSCapabilities(serviceId: string, capabilitiesXml: string): Promise<ServiceCapabilities> {
  const startTime = Date.now();
  
  try {
    const parsed = xmlParser.parse(capabilitiesXml);
    const capabilities = parsed.WMS_Capabilities || parsed.WMT_MS_Capabilities;
    
    if (!capabilities) {
      throw new Error('Invalid WMS capabilities document');
    }

    const service = capabilities.Service || {};
    const capability = capabilities.Capability || {};
    const layers = extractWMSLayers(capability.Layer);

    const result: ServiceCapabilities = {
      serviceId,
      serviceType: 'WMS',
      version: capabilities['@_version'] || config.geoserver.wmsVersion,
      title: service.Title,
      abstract: service.Abstract,
      layers,
      lastChecked: new Date(),
      responseTimeMs: Date.now() - startTime
    };

    logDiscovery({
      operation: 'parseWMSCapabilities',
      serviceId,
      layersFound: layers.length,
      durationMs: result.responseTimeMs
    });

    return result;
  } catch (error) {
    logDiscovery({
      operation: 'parseWMSCapabilities',
      serviceId,
      durationMs: Date.now() - startTime,
      error: error instanceof Error ? error.message : String(error)
    });
    throw error;
  }
}

/**
 * Parse WFS capabilities and extract layer information
 */
export async function parseWFSCapabilities(serviceId: string, capabilitiesXml: string): Promise<ServiceCapabilities> {
  const startTime = Date.now();
  
  try {
    const parsed = xmlParser.parse(capabilitiesXml);
    const capabilities = parsed.WFS_Capabilities;
    
    if (!capabilities) {
      throw new Error('Invalid WFS capabilities document');
    }

    const service = capabilities.ServiceIdentification || {};
    const featureTypeList = capabilities.FeatureTypeList || {};
    const layers = extractWFSLayers(featureTypeList.FeatureType);

    const result: ServiceCapabilities = {
      serviceId,
      serviceType: 'WFS',
      version: capabilities['@_version'] || config.geoserver.wfsVersion,
      title: service.Title,
      abstract: service.Abstract,
      layers,
      lastChecked: new Date(),
      responseTimeMs: Date.now() - startTime
    };

    logDiscovery({
      operation: 'parseWFSCapabilities',
      serviceId,
      layersFound: layers.length,
      durationMs: result.responseTimeMs
    });

    return result;
  } catch (error) {
    logDiscovery({
      operation: 'parseWFSCapabilities',
      serviceId,
      durationMs: Date.now() - startTime,
      error: error instanceof Error ? error.message : String(error)
    });
    throw error;
  }
}

/**
 * Extract layer information from WMS Layer element
 */
function extractWMSLayers(layerElement: any): LayerInfo[] {
  const layers: LayerInfo[] = [];
  
  if (!layerElement) return layers;
  
  // Handle both single layer and array of layers
  const layerArray = Array.isArray(layerElement) ? layerElement : [layerElement];
  
  for (const layer of layerArray) {
    // Skip layers without name (usually group layers)
    if (!layer.Name) {
      // Recursively process child layers
      if (layer.Layer) {
        layers.push(...extractWMSLayers(layer.Layer));
      }
      continue;
    }

    const layerInfo: LayerInfo = {
      name: layer.Name,
      title: layer.Title,
      abstract: layer.Abstract,
      workspace: extractWorkspace(layer.Name),
      styles: extractStyles(layer.Style),
      defaultStyle: extractDefaultStyle(layer.Style),
      srs: extractSRS(layer.SRS || layer.CRS),
      bbox: extractBoundingBox(layer.LatLonBoundingBox || layer.EX_GeographicBoundingBox),
      dimensions: extractDimensions(layer.Dimension)
    };

    layers.push(layerInfo);

    // Process child layers recursively
    if (layer.Layer) {
      layers.push(...extractWMSLayers(layer.Layer));
    }
  }
  
  return layers;
}

/**
 * Extract layer information from WFS FeatureType elements
 */
function extractWFSLayers(featureTypeElement: any): LayerInfo[] {
  const layers: LayerInfo[] = [];
  
  if (!featureTypeElement) return layers;
  
  const featureTypeArray = Array.isArray(featureTypeElement) ? featureTypeElement : [featureTypeElement];
  
  for (const featureType of featureTypeArray) {
    if (!featureType.Name) continue;

    const layerInfo: LayerInfo = {
      name: featureType.Name,
      title: featureType.Title,
      abstract: featureType.Abstract,
      workspace: extractWorkspace(featureType.Name),
      srs: featureType.DefaultSRS || featureType.DefaultCRS ? [featureType.DefaultSRS || featureType.DefaultCRS] : [],
      bbox: extractBoundingBox(featureType.LatLonBoundingBox || featureType.WGS84BoundingBox)
    };

    layers.push(layerInfo);
  }
  
  return layers;
}

/**
 * Extract workspace from layer name (assumes workspace:name format)
 */
function extractWorkspace(layerName: string): string {
  const parts = layerName.split(':');
  return parts.length > 1 ? parts[0] : 'default';
}

/**
 * Extract styles from WMS Layer Style element
 */
function extractStyles(styleElement: any): string[] {
  if (!styleElement) return [];
  
  const styleArray = Array.isArray(styleElement) ? styleElement : [styleElement];
  return styleArray.map((style: any) => style.Name).filter(Boolean);
}

/**
 * Extract default style
 */
function extractDefaultStyle(styleElement: any): string | undefined {
  if (!styleElement) return undefined;
  
  const styleArray = Array.isArray(styleElement) ? styleElement : [styleElement];
  return styleArray.find((style: any) => style.Name)?.Name;
}

/**
 * Extract supported SRS/CRS
 */
function extractSRS(srsElement: any): string[] {
  if (!srsElement) return [];
  
  if (Array.isArray(srsElement)) {
    return srsElement.map(String);
  }
  
  if (typeof srsElement === 'string') {
    return srsElement.split(/\s+/);
  }
  
  return [];
}

/**
 * Extract bounding box
 */
function extractBoundingBox(bboxElement: any): LayerInfo['bbox'] {
  if (!bboxElement) return undefined;
  
  // Handle different bbox formats
  if (bboxElement.westBoundLongitude !== undefined) {
    // WFS WGS84BoundingBox format
    return {
      minX: parseFloat(bboxElement.westBoundLongitude),
      minY: parseFloat(bboxElement.southBoundLatitude),
      maxX: parseFloat(bboxElement.eastBoundLongitude),
      maxY: parseFloat(bboxElement.northBoundLatitude),
      srs: 'EPSG:4326'
    };
  }
  
  if (bboxElement['@_minx'] !== undefined) {
    // WMS LatLonBoundingBox format
    return {
      minX: parseFloat(bboxElement['@_minx']),
      minY: parseFloat(bboxElement['@_miny']),
      maxX: parseFloat(bboxElement['@_maxx']),
      maxY: parseFloat(bboxElement['@_maxy']),
      srs: 'EPSG:4326'
    };
  }
  
  return undefined;
}

/**
 * Extract dimensions (time, elevation, etc.)
 */
function extractDimensions(dimensionElement: any): LayerInfo['dimensions'] {
  if (!dimensionElement) return undefined;
  
  const dimensions: LayerInfo['dimensions'] = {};
  const dimensionArray = Array.isArray(dimensionElement) ? dimensionElement : [dimensionElement];
  
  for (const dim of dimensionArray) {
    const name = dim['@_name'];
    if (!name) continue;
    
    dimensions[name] = {
      default: dim['@_default'],
      values: dim['#text'] ? dim['#text'].split(',').map((v: string) => v.trim()) : undefined
    };
  }
  
  return Object.keys(dimensions).length > 0 ? dimensions : undefined;
}

/**
 * Get geometry field for vector layer using WFS DescribeFeatureType
 */
export async function getLayerGeometryField(serviceId: string, layerName: string): Promise<{
  geometryField: string | null;
  geometryType: string | null;
}> {
  try {
    const describeXml = await describeFeatureType(serviceId, layerName);
    const parsed = xmlParser.parse(describeXml);
    
    // Navigate through the schema structure
    const schema = parsed['xsd:schema'] || parsed.schema;
    if (!schema) {
      logger.warn('No schema found in DescribeFeatureType response', { serviceId, layerName });
      return { geometryField: null, geometryType: null };
    }

    const complexType = schema['xsd:complexType'] || schema.complexType;
    if (!complexType) {
      return { geometryField: null, geometryType: null };
    }

    const sequence = complexType['xsd:sequence'] || complexType.sequence;
    if (!sequence) {
      return { geometryField: null, geometryType: null };
    }

    const elements = sequence['xsd:element'] || sequence.element;
    if (!elements) {
      return { geometryField: null, geometryType: null };
    }

    const elementArray = Array.isArray(elements) ? elements : [elements];
    
    // Look for geometry fields (gml types or common geometry field names)
    for (const element of elementArray) {
      const name = element['@_name'];
      const type = element['@_type'];
      
      if (!name || !type) continue;
      
      // Check for GML geometry types
      if (type.includes('gml:') && (
        type.includes('Geometry') || 
        type.includes('Point') || 
        type.includes('LineString') || 
        type.includes('Polygon') || 
        type.includes('MultiPoint') || 
        type.includes('MultiLineString') || 
        type.includes('MultiPolygon')
      )) {
        const geometryType = extractGeometryType(type);
        logger.debug('Geometry field detected', {
          serviceId,
          layerName,
          geometryField: name,
          geometryType
        });
        return { geometryField: name, geometryType };
      }
      
      // Check for common geometry field names
      if (['geom', 'geometry', 'the_geom', 'shape', 'wkb_geometry'].includes(name.toLowerCase())) {
        logger.debug('Geometry field detected by name', {
          serviceId,
          layerName,
          geometryField: name
        });
        return { geometryField: name, geometryType: null };
      }
    }
    
    logger.debug('No geometry field found', { serviceId, layerName });
    return { geometryField: null, geometryType: null };
  } catch (error) {
    logger.warn('Failed to get geometry field for layer', {
      serviceId,
      layerName,
      error: error instanceof Error ? error.message : String(error)
    });
    return { geometryField: null, geometryType: null };
  }
}

/**
 * Extract geometry type from GML type string
 */
function extractGeometryType(gmlType: string): string | null {
  const type = gmlType.toLowerCase();
  
  if (type.includes('point')) return 'Point';
  if (type.includes('linestring')) return 'LineString';
  if (type.includes('polygon')) return 'Polygon';
  if (type.includes('multipoint')) return 'MultiPoint';
  if (type.includes('multilinestring')) return 'MultiLineString';
  if (type.includes('multipolygon')) return 'MultiPolygon';
  if (type.includes('geometry')) return 'Geometry';
  
  return null;
}
