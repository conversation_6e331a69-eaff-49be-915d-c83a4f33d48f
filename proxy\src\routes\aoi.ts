import express, { Request, Response, NextFunction } from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import rateLimit from 'express-rate-limit';
import { config } from '../config';
import { logger, logRequest, generateTraceId } from '../lib/logging';
import { createAOI, AOIInput } from '../clipping/aoi';

const router = express.Router();

// Middleware to add trace ID
router.use((req: Request, res: Response, next: NextFunction) => {
  req.traceId = generateTraceId();
  res.setHeader('X-Trace-ID', req.traceId);
  next();
});

/**
 * POST /api/aoi
 * Create a new AOI and return ID, bounds, and centroid
 */
router.post('/aoi', async (req: Request, res: Response) => {
  const startTime = Date.now();
  
  try {
    const aoiInput: AOIInput = req.body;
    
    if (!aoiInput || (!aoiInput.geometry && !aoiInput.coordinates)) {
      return res.status(400).json({
        error: 'Invalid AOI input',
        message: 'Request body must contain geometry or coordinates',
        traceId: req.traceId
      });
    }

    const aoi = createAOI(aoiInput);

    const response = {
      aoiId: aoi.id,
      bounds: aoi.bounds,
      centroid: aoi.centroid,
      area: aoi.area,
      vertices: aoi.vertices,
      srs: aoi.srs,
      created: aoi.created
    };

    logRequest(req, res, Date.now() - startTime);
    res.status(201).json(response);
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    
    logger.error('AOI creation failed', {
      traceId: req.traceId,
      error: errorMessage,
      body: req.body
    });

    res.status(400).json({
      error: 'AOI creation failed',
      message: errorMessage,
      traceId: req.traceId
    });
  }
});

/**
 * GET /api/aoi/:id
 * Get AOI information by ID
 */
router.get('/aoi/:id', (req: Request, res: Response) => {
  try {
    const { getAOI } = require('../clipping/aoi');
    const aoi = getAOI(req.params.id);
    
    if (!aoi) {
      return res.status(404).json({
        error: 'AOI not found',
        message: `AOI with ID ${req.params.id} not found`,
        traceId: req.traceId
      });
    }

    res.json({
      aoiId: aoi.id,
      bounds: aoi.bounds,
      centroid: aoi.centroid,
      area: aoi.area,
      vertices: aoi.vertices,
      srs: aoi.srs,
      created: aoi.created
    });
  } catch (error) {
    logger.error('AOI retrieval failed', {
      traceId: req.traceId,
      aoiId: req.params.id,
      error: error instanceof Error ? error.message : String(error)
    });

    res.status(500).json({
      error: 'AOI retrieval failed',
      message: 'Internal server error',
      traceId: req.traceId
    });
  }
});

/**
 * DELETE /api/aoi/:id
 * Delete AOI from cache
 */
router.delete('/aoi/:id', (req: Request, res: Response) => {
  try {
    const { aoiCacheOps } = require('../lib/cache');
    aoiCacheOps.delete(req.params.id);

    logger.info('AOI deleted', {
      traceId: req.traceId,
      aoiId: req.params.id
    });

    res.status(204).send();
  } catch (error) {
    logger.error('AOI deletion failed', {
      traceId: req.traceId,
      aoiId: req.params.id,
      error: error instanceof Error ? error.message : String(error)
    });

    res.status(500).json({
      error: 'AOI deletion failed',
      message: 'Internal server error',
      traceId: req.traceId
    });
  }
});

/**
 * GET /api/aoi/:id/wkt
 * Get AOI as WKT in specified SRS
 */
router.get('/aoi/:id/wkt', (req: Request, res: Response) => {
  try {
    const { getAOIAsWKT } = require('../clipping/aoi');
    const targetSrs = (req.query.srs as string) || config.aoi.defaultSrs;
    
    const wkt = getAOIAsWKT(req.params.id, targetSrs);
    
    if (!wkt) {
      return res.status(404).json({
        error: 'AOI not found or WKT generation failed',
        message: `Could not generate WKT for AOI ${req.params.id} in SRS ${targetSrs}`,
        traceId: req.traceId
      });
    }

    res.set('Content-Type', 'text/plain');
    res.send(wkt);
  } catch (error) {
    logger.error('AOI WKT generation failed', {
      traceId: req.traceId,
      aoiId: req.params.id,
      srs: req.query.srs,
      error: error instanceof Error ? error.message : String(error)
    });

    res.status(500).json({
      error: 'WKT generation failed',
      message: 'Internal server error',
      traceId: req.traceId
    });
  }
});

// Extend Express Request interface
declare global {
  namespace Express {
    interface Request {
      traceId: string;
    }
  }
}

export default router;
