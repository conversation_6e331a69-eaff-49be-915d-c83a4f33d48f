import { config } from '../config';
import { logger, logClipping } from '../lib/logging';
import { getAOIAsWKT, getAOI } from './aoi';

export interface SLDClippingParams {
  layer: string;
  aoiId: string;
  targetSrs: string;
  serviceId: string;
  outputFormat?: string;
}

export interface SLDClippingResult {
  sldBody?: string;
  tier: 'sld';
  reason: string;
  postRequired: boolean;
  contentLength: number;
}

/**
 * Build SLD for raster clipping using ras:CropCoverage
 */
export function buildSLDClipping(params: SLDClippingParams): SLDClippingResult | null {
  const startTime = Date.now();
  
  try {
    // Check if SLD is enabled
    if (!config.clipping.rasterAllowSld) {
      logger.debug('SLD clipping disabled', { layer: params.layer });
      return null;
    }

    // Get AOI as WKT
    const aoiWkt = getAOIAsWKT(params.aoiId, params.targetSrs);
    if (!aoiWkt) {
      logger.error('Failed to get AOI WKT for SLD', {
        aoiId: params.aoiId,
        targetSrs: params.targetSrs
      });
      return null;
    }

    // Build SLD with ras:CropCoverage transformation
    const sldBody = buildCropCoverageSLD(params.layer, aoiWkt, params.outputFormat);
    const contentLength = sldBody.length;
    const postRequired = contentLength > config.sld.maxUrlBytes;

    const result: SLDClippingResult = {
      sldBody,
      tier: 'sld',
      reason: postRequired ? 'sld-post' : 'sld-ok',
      postRequired,
      contentLength
    };

    logClipping({
      layer: params.layer,
      tier: 'sld',
      reason: result.reason,
      aoiId: params.aoiId,
      bytes: contentLength,
      durationMs: Date.now() - startTime
    });

    return result;
  } catch (error) {
    logger.error('SLD creation failed', {
      layer: params.layer,
      aoiId: params.aoiId,
      error: error instanceof Error ? error.message : String(error)
    });
    return null;
  }
}

/**
 * Build SLD with ras:CropCoverage transformation
 */
function buildCropCoverageSLD(layerName: string, aoiWkt: string, outputFormat: string = 'image/png'): string {
  const transformationName = 'ras:CropCoverage';
  
  return `<?xml version="1.0" encoding="UTF-8"?>
<StyledLayerDescriptor version="1.0.0"
  xmlns="http://www.opengis.net/sld"
  xmlns:ogc="http://www.opengis.net/ogc"
  xmlns:xlink="http://www.w3.org/1999/xlink"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://www.opengis.net/sld StyledLayerDescriptor.xsd">
  <NamedLayer>
    <Name>${layerName}</Name>
    <UserStyle>
      <Title>AOI Crop Style</Title>
      <Abstract>Crops raster to AOI using ras:CropCoverage transformation</Abstract>
      <FeatureTypeStyle>
        <Transformation>
          <ogc:Function name="${transformationName}">
            <ogc:Function name="parameter">
              <ogc:Literal>coverage</ogc:Literal>
            </ogc:Function>
            <ogc:Function name="parameter">
              <ogc:Literal>cropShape</ogc:Literal>
              <ogc:Literal>${aoiWkt}</ogc:Literal>
            </ogc:Function>
          </ogc:Function>
        </Transformation>
        <Rule>
          <RasterSymbolizer>
            <ColorMap>
              <ColorMapEntry color="#000000" opacity="0" quantity="nodata"/>
            </ColorMap>
          </RasterSymbolizer>
        </Rule>
      </FeatureTypeStyle>
    </UserStyle>
  </NamedLayer>
</StyledLayerDescriptor>`;
}

/**
 * Build SLD with advanced clipping options
 */
export function buildAdvancedSLD(params: {
  layerName: string;
  aoiWkt: string;
  cropToGeometry?: boolean;
  preserveNoData?: boolean;
  interpolation?: 'nearest' | 'bilinear' | 'bicubic';
  outputFormat?: string;
}): string {
  const {
    layerName,
    aoiWkt,
    cropToGeometry = true,
    preserveNoData = true,
    interpolation = 'nearest',
    outputFormat = 'image/png'
  } = params;

  const transformation = cropToGeometry ? 'ras:CropCoverage' : 'ras:Clip';
  
  let transformationParams = `
            <ogc:Function name="parameter">
              <ogc:Literal>coverage</ogc:Literal>
            </ogc:Function>
            <ogc:Function name="parameter">
              <ogc:Literal>cropShape</ogc:Literal>
              <ogc:Literal>${aoiWkt}</ogc:Literal>
            </ogc:Function>`;

  if (interpolation !== 'nearest') {
    transformationParams += `
            <ogc:Function name="parameter">
              <ogc:Literal>interpolation</ogc:Literal>
              <ogc:Literal>${interpolation}</ogc:Literal>
            </ogc:Function>`;
  }

  return `<?xml version="1.0" encoding="UTF-8"?>
<StyledLayerDescriptor version="1.0.0"
  xmlns="http://www.opengis.net/sld"
  xmlns:ogc="http://www.opengis.net/ogc"
  xmlns:xlink="http://www.w3.org/1999/xlink"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://www.opengis.net/sld StyledLayerDescriptor.xsd">
  <NamedLayer>
    <Name>${layerName}</Name>
    <UserStyle>
      <Title>Advanced AOI Crop Style</Title>
      <Abstract>Advanced raster cropping with custom parameters</Abstract>
      <FeatureTypeStyle>
        <Transformation>
          <ogc:Function name="${transformation}">
            ${transformationParams}
          </ogc:Function>
        </Transformation>
        <Rule>
          <RasterSymbolizer>
            ${preserveNoData ? `
            <ColorMap>
              <ColorMapEntry color="#000000" opacity="0" quantity="nodata"/>
            </ColorMap>` : ''}
          </RasterSymbolizer>
        </Rule>
      </FeatureTypeStyle>
    </UserStyle>
  </NamedLayer>
</StyledLayerDescriptor>`;
}

/**
 * Validate SLD syntax
 */
export function validateSLD(sldXml: string): { valid: boolean; error?: string } {
  try {
    // Basic XML validation
    if (!sldXml.includes('<?xml') || !sldXml.includes('<StyledLayerDescriptor')) {
      return {
        valid: false,
        error: 'Invalid SLD structure'
      };
    }

    // Check for required elements
    const requiredElements = [
      'NamedLayer',
      'UserStyle',
      'FeatureTypeStyle',
      'Rule'
    ];

    for (const element of requiredElements) {
      if (!sldXml.includes(`<${element}`)) {
        return {
          valid: false,
          error: `Missing required element: ${element}`
        };
      }
    }

    // Check for balanced tags (basic)
    const openTags = sldXml.match(/<[^\/][^>]*>/g) || [];
    const closeTags = sldXml.match(/<\/[^>]*>/g) || [];
    
    // This is a very basic check - a full XML parser would be more accurate
    if (Math.abs(openTags.length - closeTags.length) > 2) { // Allow some self-closing tags
      return {
        valid: false,
        error: 'Unbalanced XML tags'
      };
    }

    return { valid: true };
  } catch (error) {
    return {
      valid: false,
      error: error instanceof Error ? error.message : 'Unknown validation error'
    };
  }
}

/**
 * Optimize SLD for size
 */
export function optimizeSLD(sldXml: string): string {
  return sldXml
    // Remove extra whitespace
    .replace(/>\s+</g, '><')
    .replace(/\s+/g, ' ')
    .trim()
    // Remove comments
    .replace(/<!--[\s\S]*?-->/g, '')
    // Remove unnecessary namespaces if they're not used
    .replace(/\s+xmlns:[^=]+="[^"]*"/g, (match) => {
      const namespace = match.match(/xmlns:([^=]+)=/)?.[1];
      if (namespace && !sldXml.includes(`${namespace}:`)) {
        return '';
      }
      return match;
    });
}

/**
 * Get SLD transformations available in GeoServer
 */
export function getAvailableTransformations(): string[] {
  return [
    'ras:CropCoverage',
    'ras:Clip',
    'ras:Resample',
    'ras:Rescale',
    'ras:BandSelect',
    'ras:Mosaic'
  ];
}

/**
 * Build parameter function for SLD transformations
 */
export function buildSLDParameter(name: string, value: string): string {
  return `
            <ogc:Function name="parameter">
              <ogc:Literal>${name}</ogc:Literal>
              <ogc:Literal>${value}</ogc:Literal>
            </ogc:Function>`;
}

/**
 * Escape XML special characters in SLD content
 */
export function escapeXMLContent(content: string): string {
  return content
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#39;');
}

/**
 * Check if layer supports SLD transformations
 */
export async function checkSLDSupport(serviceId: string, layerName: string): Promise<boolean> {
  // In a real implementation, this would test with a simple SLD to see if transformations are supported
  // For now, assume all GeoServer raster layers support SLD transformations
  return Promise.resolve(true);
}
