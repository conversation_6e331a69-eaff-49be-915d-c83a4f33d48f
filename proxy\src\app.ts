import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import rateLimit from 'express-rate-limit';
import morgan from 'morgan';
import { config, redactConfig } from './config';
import { logger, logRequest, generateTraceId } from './lib/logging';
import { getAllCacheStats } from './lib/cache';

// Import route handlers
import aoiRoutes from './routes/aoi';

const app = express();

// Trust proxy if behind reverse proxy
app.set('trust proxy', 1);

// Security middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      scriptSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      connectSrc: ["'self'"]
    }
  },
  crossOriginEmbedderPolicy: false // Allow serving images
}));

// CORS configuration
app.use(cors({
  origin: config.app.allowedOrigins,
  credentials: true,
  optionsSuccessStatus: 200
}));

// Request compression
app.use(compression());

// Rate limiting
const limiter = rateLimit({
  windowMs: config.security.rateLimitWindowMs,
  max: config.security.rateLimitMax,
  message: {
    error: 'Too many requests',
    message: 'Rate limit exceeded, please try again later'
  },
  standardHeaders: true,
  legacyHeaders: false
});

app.use('/api/', limiter);

// Request logging
if (config.app.nodeEnv !== 'test') {
  app.use(morgan('combined', {
    stream: {
      write: (message: string) => {
        logger.info(message.trim());
      }
    }
  }));
}

// Body parsing middleware
app.use(express.json({
  limit: `${config.security.proxyMaxBodyMb}mb`,
  strict: true
}));

app.use(express.urlencoded({
  extended: true,
  limit: `${config.security.proxyMaxBodyMb}mb`
}));

// Global trace ID middleware
app.use((req, res, next) => {
  if (!req.traceId) {
    req.traceId = generateTraceId();
  }
  res.setHeader('X-Trace-ID', req.traceId);
  next();
});

// Health check endpoint (before authentication/rate limiting)
app.get('/api/health', (req, res) => {
  const stats = getAllCacheStats();
  
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    version: '2.0.0',
    environment: config.app.nodeEnv,
    uptime: process.uptime(),
    cache: stats,
    config: {
      geoserverServices: config.geoserver.services.length,
      clipping: {
        vectorCql: config.clipping.vectorAllowCql,
        rasterSld: config.clipping.rasterAllowSld,
        rasterWps: config.clipping.rasterAllowWps
      },
      discovery: {
        wms: config.discovery.enableWms,
        wfs: config.discovery.enableWfs,
        wmts: config.discovery.enableWmts
      }
    }
  });
});

// Metrics endpoint for Prometheus
app.get('/api/metrics', (req, res) => {
  try {
    const stats = getAllCacheStats();
    
    // Basic Prometheus format metrics
    const metrics = [
      `# HELP proxy_cache_hits_total Total number of cache hits`,
      `# TYPE proxy_cache_hits_total counter`,
      `proxy_cache_hits_total{cache="tiles"} ${stats.tiles.hits || 0}`,
      `proxy_cache_hits_total{cache="aoi"} ${stats.aoi.hits || 0}`,
      `proxy_cache_hits_total{cache="capabilities"} ${stats.capabilities.hits || 0}`,
      
      `# HELP proxy_cache_misses_total Total number of cache misses`,
      `# TYPE proxy_cache_misses_total counter`,
      `proxy_cache_misses_total{cache="tiles"} ${stats.tiles.misses || 0}`,
      `proxy_cache_misses_total{cache="aoi"} ${stats.aoi.misses || 0}`,
      `proxy_cache_misses_total{cache="capabilities"} ${stats.capabilities.misses || 0}`,
      
      `# HELP proxy_cache_entries Current number of cached entries`,
      `# TYPE proxy_cache_entries gauge`,
      `proxy_cache_entries{cache="tiles"} ${stats.tiles.keys || 0}`,
      `proxy_cache_entries{cache="aoi"} ${stats.aoi.keys || 0}`,
      `proxy_cache_entries{cache="capabilities"} ${stats.capabilities.keys || 0}`,
      
      `# HELP nodejs_heap_size_used_bytes Process heap space used`,
      `# TYPE nodejs_heap_size_used_bytes gauge`,
      `nodejs_heap_size_used_bytes ${process.memoryUsage().heapUsed}`,
      
      `# HELP nodejs_heap_size_total_bytes Process heap space total`,
      `# TYPE nodejs_heap_size_total_bytes gauge`,
      `nodejs_heap_size_total_bytes ${process.memoryUsage().heapTotal}`,
      
      `# HELP process_uptime_seconds Process uptime in seconds`,
      `# TYPE process_uptime_seconds gauge`,
      `process_uptime_seconds ${process.uptime()}`,
      ''
    ].join('\n');

    res.set('Content-Type', 'text/plain; version=0.0.4; charset=utf-8');
    res.send(metrics);
  } catch (error) {
    logger.error('Metrics generation failed', {
      error: error instanceof Error ? error.message : String(error)
    });
    res.status(500).send('Internal Server Error');
  }
});

// Mount API routes
app.use('/api', aoiRoutes);

// TODO: Mount other route handlers
// app.use('/api/tiles', tileRoutes);
// app.use('/api/discovery', discoveryRoutes);
// app.use('/api', otherRoutes);

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Not Found',
    message: `Path ${req.originalUrl} not found`,
    traceId: req.traceId
  });
});

// Global error handler
app.use((error: Error, req: express.Request, res: express.Response, next: express.NextFunction) => {
  logger.error('Unhandled error', {
    traceId: req.traceId,
    error: error.message,
    stack: error.stack,
    url: req.url,
    method: req.method
  });

  // Don't leak error details in production
  const message = config.app.nodeEnv === 'production' 
    ? 'Internal server error' 
    : error.message;

  res.status(500).json({
    error: 'Internal Server Error',
    message,
    traceId: req.traceId
  });
});

// Graceful shutdown handling
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  logger.info('SIGINT received, shutting down gracefully');
  process.exit(0);
});

// Start server
const port = config.app.port;

app.listen(port, () => {
  logger.info('Map-Agnostic Proxy started', {
    port,
    nodeEnv: config.app.nodeEnv,
    logLevel: config.app.logLevel,
    geoserverServices: config.geoserver.services.length,
    config: redactConfig(config)
  });
});

// Extend Express Request interface globally
declare global {
  namespace Express {
    interface Request {
      traceId: string;
    }
  }
}

export default app;
