import NodeCache from 'node-cache';
import { config } from '../config';
import { logger } from './logging';
import fs from 'fs/promises';
import path from 'path';
import crypto from 'crypto';

// Tile cache
const tileCache = new NodeCache({
  stdTTL: config.cache.tileTtlSeconds,
  checkperiod: Math.min(config.cache.tileTtlSeconds / 2, 600), // Check every 5 minutes or half TTL
  useClones: false, // Better performance for binary data
  maxKeys: 10000 // Limit memory usage
});

// Capabilities cache
const capabilitiesCache = new NodeCache({
  stdTTL: config.cache.capabilitiesTtlSeconds,
  checkperiod: Math.min(config.cache.capabilitiesTtlSeconds / 2, 600),
  useClones: false,
  maxKeys: 100
});

// Discovery cache
const discoveryCache = new NodeCache({
  stdTTL: config.discovery.refreshSeconds,
  checkperiod: Math.min(config.discovery.refreshSeconds / 2, 600),
  useClones: false,
  maxKeys: 1000
});

// AOI cache (in-memory only)
const aoiCache = new NodeCache({
  stdTTL: 3600, // 1 hour
  checkperiod: 600, // Check every 10 minutes
  useClones: false,
  maxKeys: 1000
});

export interface CacheEntry<T> {
  data: T;
  etag: string;
  lastModified: string;
  contentType?: string;
}

export interface TileCacheKey {
  layer: string;
  z: number;
  x: number;
  y: number;
  style?: string;
  time?: string;
  srs: string;
  aoiHash?: string;
  tier: string;
  serviceId: string;
}

/**
 * Generate cache key for tiles
 */
export function generateTileCacheKey(params: TileCacheKey): string {
  const parts = [
    params.serviceId,
    params.layer,
    params.z,
    params.x,
    params.y,
    params.srs,
    params.tier
  ];

  if (params.style) parts.push(`style:${params.style}`);
  if (params.time) parts.push(`time:${params.time}`);
  if (params.aoiHash) parts.push(`aoi:${params.aoiHash}`);

  return parts.join('|');
}

/**
 * Generate ETag for response
 */
export function generateETag(data: Buffer | string): string {
  const hash = crypto.createHash('md5');
  hash.update(data);
  return `"${hash.digest('hex')}"`;
}

/**
 * Tile cache operations
 */
export const tileCacheOps = {
  get(key: string): CacheEntry<Buffer> | undefined {
    return tileCache.get<CacheEntry<Buffer>>(key);
  },

  set(key: string, data: Buffer, contentType: string = 'image/png'): void {
    const etag = generateETag(data);
    const entry: CacheEntry<Buffer> = {
      data,
      etag,
      lastModified: new Date().toUTCString(),
      contentType
    };
    
    tileCache.set(key, entry);
    
    logger.debug('Tile cached', {
      key,
      size: data.length,
      contentType,
      etag
    });
  },

  delete(key: string): void {
    tileCache.del(key);
  },

  clear(): void {
    tileCache.flushAll();
    logger.info('Tile cache cleared');
  },

  getStats() {
    return {
      keys: tileCache.keys().length,
      hits: tileCache.getStats().hits,
      misses: tileCache.getStats().misses,
      vsize: tileCache.getStats().vsize
    };
  }
};

/**
 * Capabilities cache operations
 */
export const capabilitiesCacheOps = {
  get(serviceId: string, serviceType: string): CacheEntry<string> | undefined {
    const key = `${serviceId}:${serviceType}`;
    return capabilitiesCache.get<CacheEntry<string>>(key);
  },

  set(serviceId: string, serviceType: string, data: string): void {
    const key = `${serviceId}:${serviceType}`;
    const etag = generateETag(data);
    const entry: CacheEntry<string> = {
      data,
      etag,
      lastModified: new Date().toUTCString(),
      contentType: 'application/xml'
    };
    
    capabilitiesCache.set(key, entry);
    
    logger.debug('Capabilities cached', {
      serviceId,
      serviceType,
      size: data.length,
      etag
    });
  },

  delete(serviceId: string, serviceType?: string): void {
    if (serviceType) {
      const key = `${serviceId}:${serviceType}`;
      capabilitiesCache.del(key);
    } else {
      // Delete all for this service
      const keys = capabilitiesCache.keys().filter(k => k.startsWith(`${serviceId}:`));
      capabilitiesCache.del(keys);
    }
  },

  clear(): void {
    capabilitiesCache.flushAll();
    logger.info('Capabilities cache cleared');
  }
};

/**
 * Discovery cache operations
 */
export const discoveryCacheOps = {
  get(key: string): any {
    return discoveryCache.get(key);
  },

  set(key: string, data: any): void {
    discoveryCache.set(key, data);
    logger.debug('Discovery data cached', { key, type: typeof data });
  },

  delete(key: string): void {
    discoveryCache.del(key);
  },

  clear(): void {
    discoveryCache.flushAll();
    logger.info('Discovery cache cleared');
  },

  getLayersManifest(): any[] | undefined {
    return discoveryCache.get('layers:manifest');
  },

  setLayersManifest(manifest: any[]): void {
    discoveryCache.set('layers:manifest', manifest);
    logger.info('Layers manifest cached', { layerCount: manifest.length });
  }
};

/**
 * AOI cache operations
 */
export const aoiCacheOps = {
  get(aoiId: string): any {
    return aoiCache.get(aoiId);
  },

  set(aoiId: string, aoiData: any): void {
    aoiCache.set(aoiId, aoiData);
    logger.debug('AOI cached', { aoiId, vertices: aoiData.geometry?.coordinates?.[0]?.length });
  },

  delete(aoiId: string): void {
    aoiCache.del(aoiId);
  },

  clear(): void {
    aoiCache.flushAll();
    logger.info('AOI cache cleared');
  },

  getStats() {
    return {
      keys: aoiCache.keys().length,
      hits: aoiCache.getStats().hits,
      misses: aoiCache.getStats().misses
    };
  }
};

/**
 * WPS file cache operations (disk-based)
 */
export class WpsFileCache {
  private cacheDir: string;
  private maxSizeBytes: number;
  private indexFile: string;
  private index: Map<string, { file: string; size: number; created: number; accessed: number }>;

  constructor(
    cacheDir: string = config.wps.cacheDir,
    maxSizeBytes: number = 1024 * 1024 * 1024 // 1GB default
  ) {
    this.cacheDir = path.resolve(cacheDir);
    this.maxSizeBytes = maxSizeBytes;
    this.indexFile = path.join(this.cacheDir, 'index.json');
    this.index = new Map();
    this.loadIndex();
  }

  private async loadIndex(): Promise<void> {
    try {
      await fs.mkdir(this.cacheDir, { recursive: true });
      
      try {
        const indexData = await fs.readFile(this.indexFile, 'utf-8');
        const indexJson = JSON.parse(indexData);
        this.index = new Map(Object.entries(indexJson));
      } catch (error) {
        // Index file doesn't exist or is corrupted, start fresh
        this.index = new Map();
      }
    } catch (error) {
      logger.error('Failed to initialize WPS cache', {
        cacheDir: this.cacheDir,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  private async saveIndex(): Promise<void> {
    try {
      const indexJson = Object.fromEntries(this.index);
      await fs.writeFile(this.indexFile, JSON.stringify(indexJson, null, 2));
    } catch (error) {
      logger.error('Failed to save WPS cache index', {
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  async get(key: string): Promise<Buffer | undefined> {
    const entry = this.index.get(key);
    if (!entry) return undefined;

    try {
      const filePath = path.join(this.cacheDir, entry.file);
      const data = await fs.readFile(filePath);
      
      // Update access time
      entry.accessed = Date.now();
      this.index.set(key, entry);
      
      logger.debug('WPS cache hit', { key, size: data.length });
      return data;
    } catch (error) {
      // File doesn't exist, remove from index
      this.index.delete(key);
      logger.warn('WPS cache file missing, removed from index', { key });
      return undefined;
    }
  }

  async set(key: string, data: Buffer): Promise<void> {
    try {
      // Generate unique filename
      const hash = crypto.createHash('sha256').update(key).digest('hex');
      const filename = `${hash}.bin`;
      const filePath = path.join(this.cacheDir, filename);

      // Write file
      await fs.writeFile(filePath, data);

      // Update index
      const entry = {
        file: filename,
        size: data.length,
        created: Date.now(),
        accessed: Date.now()
      };
      this.index.set(key, entry);

      // Clean up if over size limit
      await this.cleanup();
      
      // Save index
      await this.saveIndex();

      logger.debug('WPS result cached to disk', {
        key,
        filename,
        size: data.length
      });
    } catch (error) {
      logger.error('Failed to cache WPS result', {
        key,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  async delete(key: string): Promise<void> {
    const entry = this.index.get(key);
    if (!entry) return;

    try {
      const filePath = path.join(this.cacheDir, entry.file);
      await fs.unlink(filePath);
      this.index.delete(key);
      await this.saveIndex();
      
      logger.debug('WPS cache entry deleted', { key });
    } catch (error) {
      logger.warn('Failed to delete WPS cache file', {
        key,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  async clear(): Promise<void> {
    try {
      // Delete all cache files
      for (const [key, entry] of this.index) {
        try {
          const filePath = path.join(this.cacheDir, entry.file);
          await fs.unlink(filePath);
        } catch (error) {
          // File might already be deleted
        }
      }

      this.index.clear();
      await this.saveIndex();
      
      logger.info('WPS cache cleared');
    } catch (error) {
      logger.error('Failed to clear WPS cache', {
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  private async cleanup(): Promise<void> {
    const totalSize = Array.from(this.index.values())
      .reduce((sum, entry) => sum + entry.size, 0);

    if (totalSize <= this.maxSizeBytes) return;

    // Sort by access time (LRU)
    const entries = Array.from(this.index.entries())
      .sort(([, a], [, b]) => a.accessed - b.accessed);

    let removedSize = 0;
    const targetRemovalSize = totalSize - this.maxSizeBytes;

    for (const [key, entry] of entries) {
      if (removedSize >= targetRemovalSize) break;

      try {
        const filePath = path.join(this.cacheDir, entry.file);
        await fs.unlink(filePath);
        this.index.delete(key);
        removedSize += entry.size;
        
        logger.debug('WPS cache entry evicted (LRU)', {
          key,
          size: entry.size,
          age: Date.now() - entry.created
        });
      } catch (error) {
        // File already deleted, just remove from index
        this.index.delete(key);
      }
    }

    logger.info('WPS cache cleanup completed', {
      entriesRemoved: entries.length,
      bytesRemoved: removedSize,
      remainingEntries: this.index.size
    });
  }

  getStats() {
    const entries = Array.from(this.index.values());
    const totalSize = entries.reduce((sum, entry) => sum + entry.size, 0);
    
    return {
      entries: this.index.size,
      totalSizeBytes: totalSize,
      maxSizeBytes: this.maxSizeBytes,
      utilizationPercent: (totalSize / this.maxSizeBytes) * 100
    };
  }
}

// Global WPS cache instance
export const wpsCache = new WpsFileCache();

/**
 * Get cache statistics for monitoring
 */
export function getAllCacheStats() {
  return {
    tiles: tileCacheOps.getStats(),
    aoi: aoiCacheOps.getStats(),
    wps: wpsCache.getStats(),
    capabilities: {
      keys: capabilitiesCache.keys().length,
      hits: capabilitiesCache.getStats().hits,
      misses: capabilitiesCache.getStats().misses
    },
    discovery: {
      keys: discoveryCache.keys().length,
      hits: discoveryCache.getStats().hits,
      misses: discoveryCache.getStats().misses
    }
  };
}
