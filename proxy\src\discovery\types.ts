export interface LayerManifest {
  id: string;
  workspace: string;
  name: string;
  title: string;
  abstract?: string;
  serviceTypes: ('WMS' | 'WFS' | 'WMTS')[];
  
  type: 'vector' | 'raster' | 'tile';
  
  nativeSRS: string;
  defaultSRS: string;
  latLonBBox: {
    minX: number;
    minY: number;
    maxX: number;
    maxY: number;
  };
  scaleDenominators?: {
    min?: number;
    max?: number;
  };

  // Vector-specific
  geometryField?: string;
  geometryType?: string;

  // Raster-specific
  format?: string;
  bands?: {
    names?: string[];
    count?: number;
  };

  // Dimensions
  dimensions?: {
    time?: {
      enabled: boolean;
      default?: string;
      values?: string[];
      range?: {
        start: string;
        end: string;
        interval?: string;
      };
    };
    elevation?: {
      enabled: boolean;
      default?: number;
      values?: number[];
      range?: {
        min: number;
        max: number;
        interval?: number;
      };
    };
  };

  // Styles
  styles: {
    defaultStyle: string;
    availableStyles: string[];
    legendURL?: string;
  };

  // Capabilities flags
  ops: {
    supportsCQL: boolean;
    supportsSLD: boolean;
    supportsWPS: boolean;
    supportsTiled: boolean;
  };

  // Clipping hints
  clippingHints: {
    vectorClip: 'cql' | 'bbox';
    rasterClip: 'sld' | 'wps' | 'mask';
  };

  // Categorization
  categorization: {
    category?: string;
    tags: string[];
    isRemote: boolean;
  };

  // Health
  health: {
    lastChecked: Date;
    status: 'ok' | 'degraded' | 'down';
    latencyMs?: number;
  };

  // Metadata
  metadata: {
    attribution?: string;
    keywords: string[];
    metadataURL?: string;
  };

  // Source info
  sourceService: {
    id: string;
    url: string;
    type: 'WMS' | 'WFS' | 'WMTS';
  };
}

export interface ServiceCapabilities {
  serviceId: string;
  serviceType: 'WMS' | 'WFS' | 'WMTS';
  version: string;
  title?: string;
  abstract?: string;
  layers: LayerInfo[];
  lastChecked: Date;
  responseTimeMs: number;
}

export interface LayerInfo {
  name: string;
  title?: string;
  abstract?: string;
  workspace?: string;
  styles?: string[];
  defaultStyle?: string;
  srs?: string[];
  bbox?: {
    minX: number;
    minY: number;
    maxX: number;
    maxY: number;
    srs: string;
  };
  dimensions?: {
    [key: string]: {
      default?: string;
      values?: string[];
    };
  };
}

export interface DiscoveryJob {
  id: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  progress: {
    current: number;
    total: number;
    currentTask?: string;
  };
  startTime: Date;
  endTime?: Date;
  error?: string;
  results?: {
    servicesChecked: number;
    layersFound: number;
    layersUpdated: number;
    errors: number;
  };
}

export interface DiscoveryOverride {
  layerId: string; // workspace:name format
  override: Partial<LayerManifest>;
  created: Date;
  updated: Date;
  author: string;
  reason?: string;
}
