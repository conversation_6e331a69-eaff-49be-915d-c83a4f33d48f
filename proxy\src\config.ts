import { z } from 'zod';
import dotenv from 'dotenv';

// Load .env file
dotenv.config();

// GeoServer service schema
const GeoServerServiceSchema = z.object({
  id: z.string().min(1),
  url: z.string().url(),
  username: z.string().min(1),
  password: z.string().min(1),
  allowSelfSigned: z.boolean().default(false)
});

// Main config schema
const ConfigSchema = z.object({
  // App
  app: z.object({
    port: z.coerce.number().int().min(1).max(65535).default(3001),
    nodeEnv: z.enum(['development', 'production', 'test']).default('development'),
    logLevel: z.enum(['error', 'warn', 'info', 'debug']).default('info'),
    allowedOrigins: z.array(z.string()).default(['http://localhost:5173'])
  }),

  // GeoServer
  geoserver: z.object({
    services: z.array(GeoServerServiceSchema).min(1),
    wmsVersion: z.string().default('1.3.0'),
    wfsVersion: z.string().default('2.0.0'),
    wmtsEnabled: z.boolean().default(true),
    timeoutMs: z.coerce.number().int().positive().default(15000)
  }),

  // AOI
  aoi: z.object({
    maxVertices: z.coerce.number().int().positive().default(10000),
    wktLengthThreshold: z.coerce.number().int().positive().default(50000),
    simplificationTolerance: z.coerce.number().positive().default(0.0001),
    defaultSrs: z.string().default('EPSG:4326')
  }),

  // Clipping
  clipping: z.object({
    vectorAllowCql: z.boolean().default(true),
    rasterAllowSld: z.boolean().default(true),
    rasterAllowWps: z.boolean().default(true)
  }),

  // SLD/WPS
  sld: z.object({
    maxUrlBytes: z.coerce.number().int().positive().default(2000)
  }),

  wps: z.object({
    timeoutMs: z.coerce.number().int().positive().default(60000),
    pollIntervalMs: z.coerce.number().int().positive().default(1500),
    cacheDir: z.string().default('.cache/wps')
  }),

  // Discovery
  discovery: z.object({
    enableWms: z.boolean().default(true),
    enableWfs: z.boolean().default(true),
    enableWmts: z.boolean().default(true),
    refreshSeconds: z.coerce.number().int().positive().default(600),
    parallel: z.coerce.number().int().positive().default(6)
  }),

  // Caching
  cache: z.object({
    tileTtlSeconds: z.coerce.number().int().positive().default(300),
    capabilitiesTtlSeconds: z.coerce.number().int().positive().default(600)
  }),

  // Security
  security: z.object({
    rateLimitWindowMs: z.coerce.number().int().positive().default(60000),
    rateLimitMax: z.coerce.number().int().positive().default(120),
    proxyMaxBodyMb: z.coerce.number().positive().default(25)
  }),

  // TLS
  tls: z.object({
    rejectUnauthorized: z.boolean().default(false)
  })
});

// Parse environment variables
function parseEnvArray(envVar: string | undefined, defaultValue: any[] = []): any[] {
  if (!envVar) return defaultValue;
  try {
    return JSON.parse(envVar);
  } catch (error) {
    throw new Error(`Failed to parse ${envVar}: ${error}`);
  }
}

function parseEnvBoolean(envVar: string | undefined, defaultValue: boolean = false): boolean {
  if (!envVar) return defaultValue;
  return envVar === '1' || envVar.toLowerCase() === 'true';
}

// Build config object from environment variables
const rawConfig = {
  app: {
    port: process.env.APP_PORT,
    nodeEnv: process.env.NODE_ENV,
    logLevel: process.env.LOG_LEVEL,
    allowedOrigins: parseEnvArray(process.env.ALLOWED_ORIGINS)
  },
  geoserver: {
    services: parseEnvArray(process.env.GEOSERVER_SERVICES),
    wmsVersion: process.env.GEOSERVER_WMS_VERSION,
    wfsVersion: process.env.GEOSERVER_WFS_VERSION,
    wmtsEnabled: parseEnvBoolean(process.env.GEOSERVER_WMTS_ENABLED),
    timeoutMs: process.env.GEOSERVER_TIMEOUT_MS
  },
  aoi: {
    maxVertices: process.env.AOI_MAX_VERTICES,
    wktLengthThreshold: process.env.AOI_WKT_LENGTH_THRESHOLD,
    simplificationTolerance: process.env.AOI_SIMPLIFICATION_TOLERANCE,
    defaultSrs: process.env.DEFAULT_SRS
  },
  clipping: {
    vectorAllowCql: parseEnvBoolean(process.env.CLIP_VECTOR_ALLOW_CQL),
    rasterAllowSld: parseEnvBoolean(process.env.CLIP_RASTER_ALLOW_SLD),
    rasterAllowWps: parseEnvBoolean(process.env.CLIP_RASTER_ALLOW_WPS)
  },
  sld: {
    maxUrlBytes: process.env.SLD_MAX_URL_BYTES
  },
  wps: {
    timeoutMs: process.env.WPS_TIMEOUT_MS,
    pollIntervalMs: process.env.WPS_POLL_INTERVAL_MS,
    cacheDir: process.env.WPS_CACHE_DIR
  },
  discovery: {
    enableWms: parseEnvBoolean(process.env.DISCOVERY_ENABLE_WMS),
    enableWfs: parseEnvBoolean(process.env.DISCOVERY_ENABLE_WFS),
    enableWmts: parseEnvBoolean(process.env.DISCOVERY_ENABLE_WMTS),
    refreshSeconds: process.env.DISCOVERY_REFRESH_SECONDS,
    parallel: process.env.DISCOVERY_PARALLEL
  },
  cache: {
    tileTtlSeconds: process.env.TILE_CACHE_TTL_SECONDS,
    capabilitiesTtlSeconds: process.env.CAPABILITIES_TTL_SECONDS
  },
  security: {
    rateLimitWindowMs: process.env.RATE_LIMIT_WINDOW_MS,
    rateLimitMax: process.env.RATE_LIMIT_MAX,
    proxyMaxBodyMb: process.env.PROXY_MAX_BODY_MB
  },
  tls: {
    rejectUnauthorized: !parseEnvBoolean(process.env.NODE_TLS_REJECT_UNAUTHORIZED, true)
  }
};

// Validate and parse config
let config: z.infer<typeof ConfigSchema>;

try {
  config = ConfigSchema.parse(rawConfig);
} catch (error) {
  if (error instanceof z.ZodError) {
    console.error('❌ Configuration validation failed:');
    for (const issue of error.issues) {
      const path = issue.path.join('.');
      console.error(`  - ${path}: ${issue.message}`);
    }
    console.error('\n💡 Please check your .env file against .env.example');
    process.exit(1);
  }
  throw error;
}

// Validate that GeoServer service IDs are unique
const serviceIds = config.geoserver.services.map(s => s.id);
const uniqueServiceIds = new Set(serviceIds);
if (serviceIds.length !== uniqueServiceIds.size) {
  console.error('❌ GeoServer service IDs must be unique');
  process.exit(1);
}

// Set up TLS rejection
if (!config.tls.rejectUnauthorized) {
  process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';
}

// Export types
export type Config = z.infer<typeof ConfigSchema>;
export type GeoServerService = z.infer<typeof GeoServerServiceSchema>;

// Export config
export { config };

// Helper to redact sensitive fields for logging
export function redactConfig(cfg: Config): Partial<Config> {
  return {
    ...cfg,
    geoserver: {
      ...cfg.geoserver,
      services: cfg.geoserver.services.map(service => ({
        ...service,
        username: '[REDACTED]',
        password: '[REDACTED]'
      }))
    }
  };
}

// Helper to get service by ID
export function getGeoServerService(id: string): GeoServerService {
  const service = config.geoserver.services.find(s => s.id === id);
  if (!service) {
    throw new Error(`GeoServer service not found: ${id}`);
  }
  return service;
}

// Helper to get all service IDs
export function getAllGeoServerServiceIds(): string[] {
  return config.geoserver.services.map(s => s.id);
}
