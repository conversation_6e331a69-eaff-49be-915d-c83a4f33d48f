{"name": "map-agnostic-proxy", "version": "2.0.0", "description": "Map-agnostic proxy for Leaflet and OpenLayers frontends with AOI-true clipping", "main": "dist/app.js", "scripts": {"build": "tsc", "start": "node dist/app.js", "dev": "ts-node-dev --respawn --transpile-only src/app.ts", "test": "jest", "lint": "eslint src/**/*.ts", "clean": "<PERSON><PERSON><PERSON> dist"}, "keywords": ["proxy", "geoserver", "leaflet", "openlayers", "aoi", "clipping", "wms", "wfs", "wmts"], "dependencies": {"express": "^4.18.2", "axios": "^1.6.0", "dotenv": "^16.3.1", "zod": "^3.22.4", "cors": "^2.8.5", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "compression": "^1.7.4", "morgan": "^1.10.0", "node-cache": "^5.1.2", "proj4": "^2.9.2", "xml2js": "^0.6.2", "uuid": "^9.0.1", "fast-xml-parser": "^4.3.2", "multer": "^1.4.5-lts.1", "archiver": "^6.0.1", "prom-client": "^15.0.0"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/compression": "^1.7.5", "@types/morgan": "^1.9.9", "@types/node": "^20.9.0", "@types/xml2js": "^0.4.14", "@types/uuid": "^9.0.7", "@types/multer": "^1.4.11", "@types/archiver": "^6.0.2", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "eslint": "^8.54.0", "jest": "^29.7.0", "@types/jest": "^29.5.8", "ts-jest": "^29.1.1", "ts-node-dev": "^2.0.0", "typescript": "^5.2.2", "rimraf": "^5.0.5"}, "engines": {"node": ">=18.0.0"}}