import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { Agent as HttpsAgent } from 'https';
import { config, GeoServerService } from '../config';
import { logger, logGeoServerRequest } from './logging';

export interface GeoServerClient {
  get(path: string, params?: Record<string, any>): Promise<AxiosResponse>;
  post(path: string, data?: any, contentType?: string): Promise<AxiosResponse>;
  getService(): GeoServerService;
}

class GeoServerClientImpl implements GeoServerClient {
  private client: AxiosInstance;
  private service: GeoServerService;

  constructor(service: GeoServerService) {
    this.service = service;
    
    // Create HTTPS agent with custom settings
    const httpsAgent = new HttpsAgent({
      rejectUnauthorized: !service.allowSelfSigned
    });

    // Create axios instance with service-specific configuration
    this.client = axios.create({
      baseURL: service.url,
      timeout: config.geoserver.timeoutMs,
      httpsAgent,
      auth: {
        username: service.username,
        password: service.password
      },
      headers: {
        'User-Agent': 'Map-Agnostic-Proxy/2.0.0'
      }
    });

    // Request interceptor for logging
    this.client.interceptors.request.use(
      (config) => {
        config.metadata = { startTime: Date.now() };
        return config;
      },
      (error) => {
        logger.error('GeoServer request setup failed', {
          serviceId: service.id,
          error: error.message
        });
        return Promise.reject(error);
      }
    );

    // Response interceptor for logging
    this.client.interceptors.response.use(
      (response) => {
        const startTime = response.config.metadata?.startTime || Date.now();
        const duration = Date.now() - startTime;
        
        logGeoServerRequest({
          serviceId: service.id,
          method: response.config.method?.toUpperCase() || 'GET',
          url: response.config.url || '',
          statusCode: response.status,
          durationMs: duration
        });

        return response;
      },
      (error) => {
        const startTime = error.config?.metadata?.startTime || Date.now();
        const duration = Date.now() - startTime;
        
        logGeoServerRequest({
          serviceId: service.id,
          method: error.config?.method?.toUpperCase() || 'GET',
          url: error.config?.url || '',
          statusCode: error.response?.status,
          durationMs: duration,
          error: error.message
        });

        return Promise.reject(error);
      }
    );
  }

  async get(path: string, params?: Record<string, any>): Promise<AxiosResponse> {
    const config: AxiosRequestConfig = {
      params: params || {}
    };

    return this.client.get(path, config);
  }

  async post(path: string, data?: any, contentType: string = 'application/x-www-form-urlencoded'): Promise<AxiosResponse> {
    const config: AxiosRequestConfig = {
      headers: {
        'Content-Type': contentType
      }
    };

    return this.client.post(path, data, config);
  }

  getService(): GeoServerService {
    return this.service;
  }
}

// Client factory and cache
const clientCache = new Map<string, GeoServerClient>();

export function getGeoServerClient(serviceId: string): GeoServerClient {
  if (!clientCache.has(serviceId)) {
    const service = config.geoserver.services.find(s => s.id === serviceId);
    if (!service) {
      throw new Error(`GeoServer service not found: ${serviceId}`);
    }
    clientCache.set(serviceId, new GeoServerClientImpl(service));
  }
  
  return clientCache.get(serviceId)!;
}

export function getAllGeoServerClients(): Map<string, GeoServerClient> {
  // Ensure all clients are created
  for (const service of config.geoserver.services) {
    if (!clientCache.has(service.id)) {
      clientCache.set(service.id, new GeoServerClientImpl(service));
    }
  }
  
  return clientCache;
}

// Utility functions for common GeoServer operations
export async function getCapabilities(serviceId: string, serviceType: 'WMS' | 'WFS' | 'WMTS'): Promise<string> {
  const client = getGeoServerClient(serviceId);
  
  const params: Record<string, any> = {
    service: serviceType,
    request: 'GetCapabilities'
  };

  if (serviceType === 'WMS') {
    params.version = config.geoserver.wmsVersion;
  } else if (serviceType === 'WFS') {
    params.version = config.geoserver.wfsVersion;
  }

  const response = await client.get('/ows', params);
  return response.data;
}

export async function describeFeatureType(serviceId: string, typeName: string): Promise<string> {
  const client = getGeoServerClient(serviceId);
  
  const response = await client.get('/ows', {
    service: 'WFS',
    version: config.geoserver.wfsVersion,
    request: 'DescribeFeatureType',
    typeName
  });
  
  return response.data;
}

export async function getMap(
  serviceId: string,
  params: {
    layers: string;
    styles?: string;
    srs: string;
    bbox: string;
    width: number;
    height: number;
    format?: string;
    transparent?: boolean;
    time?: string;
    cql_filter?: string;
    sld_body?: string;
  }
): Promise<AxiosResponse> {
  const client = getGeoServerClient(serviceId);
  
  const requestParams = {
    service: 'WMS',
    version: config.geoserver.wmsVersion,
    request: 'GetMap',
    format: 'image/png',
    transparent: true,
    ...params
  };

  // Check if SLD_BODY is too large for GET request
  const sldBody = requestParams.sld_body;
  if (sldBody && sldBody.length > config.sld.maxUrlBytes) {
    // Convert to POST request
    const { sld_body, ...getParams } = requestParams;
    const postData = new URLSearchParams({
      ...getParams,
      SLD_BODY: sld_body
    }).toString();
    
    return client.post('/ows', postData, 'application/x-www-form-urlencoded');
  }

  return client.get('/ows', requestParams);
}

export async function getFeatureInfo(
  serviceId: string,
  params: {
    layers: string;
    query_layers: string;
    styles?: string;
    srs: string;
    bbox: string;
    width: number;
    height: number;
    x: number;
    y: number;
    info_format?: string;
    feature_count?: number;
    cql_filter?: string;
  }
): Promise<AxiosResponse> {
  const client = getGeoServerClient(serviceId);
  
  const requestParams = {
    service: 'WMS',
    version: config.geoserver.wmsVersion,
    request: 'GetFeatureInfo',
    info_format: 'application/json',
    feature_count: 10,
    ...params
  };

  return client.get('/ows', requestParams);
}

// WPS operation helpers
export async function executeWpsProcess(
  serviceId: string,
  processId: string,
  inputs: Record<string, any>,
  async: boolean = true
): Promise<AxiosResponse> {
  const client = getGeoServerClient(serviceId);
  
  // Build WPS execute request XML
  const executeXml = buildWpsExecuteXml(processId, inputs, async);
  
  return client.post('/ows', executeXml, 'application/xml');
}

export async function getWpsStatus(serviceId: string, jobId: string): Promise<AxiosResponse> {
  const client = getGeoServerClient(serviceId);
  
  return client.get(`/ows?service=WPS&version=1.0.0&request=GetExecutionStatus&executionId=${jobId}`);
}

function buildWpsExecuteXml(processId: string, inputs: Record<string, any>, async: boolean): string {
  const inputsXml = Object.entries(inputs)
    .map(([key, value]) => {
      if (typeof value === 'string') {
        return `<wps:Input>
          <ows:Identifier>${key}</ows:Identifier>
          <wps:Data>
            <wps:LiteralData>${value}</wps:LiteralData>
          </wps:Data>
        </wps:Input>`;
      } else if (value && typeof value === 'object' && value.mimeType) {
        return `<wps:Input>
          <ows:Identifier>${key}</ows:Identifier>
          <wps:Data>
            <wps:ComplexData mimeType="${value.mimeType}">${value.data}</wps:ComplexData>
          </wps:Data>
        </wps:Input>`;
      }
      return '';
    })
    .join('\n');

  return `<?xml version="1.0" encoding="UTF-8"?>
<wps:Execute version="1.0.0" service="WPS" 
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xmlns:wps="http://www.opengis.net/wps/1.0.0"
  xmlns:ows="http://www.opengis.net/ows/1.1"
  xsi:schemaLocation="http://www.opengis.net/wps/1.0.0 http://schemas.opengis.net/wps/1.0.0/wpsAll.xsd">
  <ows:Identifier>${processId}</ows:Identifier>
  <wps:DataInputs>
    ${inputsXml}
  </wps:DataInputs>
  <wps:ResponseForm>
    <wps:ResponseDocument ${async ? 'storeExecuteResponse="true" status="true"' : ''}>
      <wps:Output>
        <ows:Identifier>result</ows:Identifier>
      </wps:Output>
    </wps:ResponseDocument>
  </wps:ResponseForm>
</wps:Execute>`;
}

// Extend AxiosRequestConfig to include metadata
declare module 'axios' {
  interface AxiosRequestConfig {
    metadata?: {
      startTime: number;
    };
  }
}
